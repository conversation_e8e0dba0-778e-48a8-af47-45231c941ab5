'use client';

import { useState, useMemo, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import { DarkModeToggle } from './DarkModeToggle';
import './enhanced-styles.css';
import type {
  EvaluatedDevice,
  EvaluationGrade,
  ScreenGrade,
  NetworkGrade,
  FinalGrade,
  FaultType,
  DamageType,
  EvaluationOrder,
  SystemSettings,
} from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  PlusCircle,
  Trash2,
  Save,
  FileDown,
  Printer,
  X,
  Upload,
  FileSpreadsheet,
  FolderOpen,
  Trash,
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const initialFormState = {
  imei: '',
  externalGrade: 'بدون' as EvaluationGrade,
  screenGrade: 'بدون' as ScreenGrade,
  networkGrade: 'مفتوح رسمي' as NetworkGrade,
  finalGrade: 'جاهز للبيع' as FinalGrade,
  fault: '' as FaultType | '',
  customFault: '',
  damage: '' as DamageType | '',
  customDamage: '',
};

export default function GradingPage() {
  const {
    devices,
    addEvaluationOrder,
    evaluationOrders,
    deleteEvaluationOrder,
    updateEvaluationOrder,
    systemSettings,
    currentUser,
  } = useStore();
  const { toast } = useToast();

  // 🔐 نظام الصلاحيات
  const canView = currentUser?.permissions?.grading?.view ?? false;
  const canCreate = currentUser?.permissions?.grading?.create ?? false;
  const canEdit = currentUser?.permissions?.grading?.edit ?? false;
  const canDelete = currentUser?.permissions?.grading?.delete ?? false;

  // منع الوصول للصفحة بدون صلاحية العرض
  if (!canView) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600">غير مصرح لك بالوصول</h2>
          <p className="text-gray-500 mt-2">ليس لديك صلاحية لعرض صفحة التقييم</p>
        </div>
      </div>
    );
  }

  // 📝 وضع الإنشاء (Create Mode)
  const [isCreating, setIsCreating] = useState(false);

  const [evaluationId, setEvaluationId] = useState('');
  const [imei, setImei] = useState('');
  const [currentDevice, setCurrentDevice] = useState<{
    id: string;
    model: string;
  } | null>(null);
  const [formState, setFormState] = useState(initialFormState);
  const [evaluatedItems, setEvaluatedItems] = useState<EvaluatedDevice[]>([]);
  const [isCancelAlertOpen, setIsCancelAlertOpen] = useState(false);
  const [generalNotes, setGeneralNotes] = useState('');
  const [previousEvaluations, setPreviousEvaluations] = useState<
    EvaluationOrder[]
  >([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isLoadOrderDialogOpen, setIsLoadOrderDialogOpen] = useState(false);
  const [loadedOrder, setLoadedOrder] = useState<EvaluationOrder | null>(null);
  const [orderToDelete, setOrderToDelete] = useState<EvaluationOrder | null>(
    null,
  );

  // متغيرات المسودة
  const [isDraft, setIsDraft] = useState(false);
  const [hasSavedDraft, setHasSavedDraft] = useState(false);

  // التحقق من وجود مسودة محفوظة
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const draftExists = localStorage.getItem('evaluationOrderDraft');
      setHasSavedDraft(!!draftExists);
    }
  }, []);

  // حفظ المسودة
  const saveDraft = () => {
    if (evaluatedItems.length === 0 && !imei) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لا توجد بيانات كافية للحفظ كمسودة.',
      });
      return;
    }

    const draftData = {
      evaluationId,
      imei,
      currentDevice,
      formState,
      evaluatedItems,
      generalNotes,
      timestamp: new Date().toISOString(),
    };

    try {
      localStorage.setItem('evaluationOrderDraft', JSON.stringify(draftData));
      setIsDraft(true);
      setHasSavedDraft(true);

      toast({
        title: 'تم حفظ المسودة',
        description: `تم حفظ بيانات أمر التقييم ${evaluationId} كمسودة بنجاح.`,
      });
    } catch (error) {
      console.error('Error saving draft:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ المسودة. يرجى المحاولة مرة أخرى.',
      });
    }
  };

  // تحميل المسودة
  const loadDraft = () => {
    try {
      if (typeof window !== 'undefined') {
        const draftJSON = localStorage.getItem('evaluationOrderDraft');
        if (!draftJSON) {
          toast({
            variant: 'destructive',
            title: 'لا توجد مسودة',
            description: 'لم يتم العثور على أي مسودة محفوظة.',
          });
          return;
        }

        const draftData = JSON.parse(draftJSON);

        setEvaluationId(draftData.evaluationId || '');
        setImei(draftData.imei || '');
        setCurrentDevice(draftData.currentDevice || null);
        setFormState(draftData.formState || initialFormState);
        setEvaluatedItems(draftData.evaluatedItems || []);
        setGeneralNotes(draftData.generalNotes || '');
        setLoadedOrder(null);
        setIsDraft(true);

        toast({
          title: 'تم تحميل المسودة',
          description: `تم تحميل المسودة المحفوظة بتاريخ ${new Date(draftData.timestamp).toLocaleString('ar-EG')}`,
        });
      }
    } catch (error) {
      console.error('Error loading draft:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ في التحميل',
        description: 'حدث خطأ أثناء تحميل المسودة. قد تكون البيانات تالفة.',
      });
    }
  };

  // حذف المسودة وتعطيل الأزرار
  const clearDraft = () => {
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('evaluationOrderDraft');
        setIsDraft(false);
        setHasSavedDraft(false);
      }
    } catch (error) {
      console.error('Error clearing draft:', error);
    }
  };

  // دالة تنسيق التاريخ والوقت
  const formatDateTime = (dateTimeString: string): string => {
    if (!dateTimeString) return '';

    try {
      const date = new Date(dateTimeString);
      if (isNaN(date.getTime())) return dateTimeString;

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      return dateTimeString;
    }
  };

  const generateNewEvaluationId = () => {
    if (!evaluationOrders) return '';

    // ترقيم تسلسلي بدء من 1 - البحث عن أعلى رقم مستخدم عالمياً
    let maxNumber = 0;

    evaluationOrders.forEach(order => {
      if (order.orderId) {
        // استخراج الرقم من نهاية معرف الأمر (EVAL-1, EVAL-2, إلخ)
        const match = order.orderId.match(/EVAL-(\d+)$/);
        if (match) {
          const numberPart = parseInt(match[1]);
          if (!isNaN(numberPart) && numberPart > maxNumber) {
            maxNumber = numberPart;
          }
        }
      }
    });

    // إنشاء مجموعة من الأرقام المستخدمة للتحقق السريع
    const usedNumbers = new Set(
      evaluationOrders
        .map(order => {
          const match = order.orderId.match(/EVAL-(\d+)$/);
          return match ? parseInt(match[1]) : 0;
        })
        .filter(num => num > 0)
    );

    // البحث عن أول رقم متاح
    for (let i = 1; i <= maxNumber + 1; i++) {
      if (!usedNumbers.has(i)) {
        return `EVAL-${i}`; // بدون padding، رقم عادي
      }
    }

    // في حالة عدم وجود أرقام، ابدأ من 1
    return 'EVAL-1';
  };

  useEffect(() => {
    if (loadedOrder) {
      setEvaluationId(loadedOrder.orderId);
      return;
    }
    if (!evaluationOrders) return;

    const newId = generateNewEvaluationId();
    if (newId) {
      setEvaluationId(newId);
    }
  }, [evaluationOrders, loadedOrder]);

  const handleImeiSearch = () => {
    if (!imei) return;

    const device = devices.find((d) => d.id === imei);
    if (!device) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'الجهاز غير موجود في النظام.',
      });
      setCurrentDevice(null);
      return;
    }
    if (device.status === 'مباع') {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لا يمكن تقييم جهاز تم بيعه.',
      });
      setCurrentDevice(null);
      return;
    }
    if (evaluatedItems.some((item) => item.deviceId === imei)) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'تم تقييم هذا الجهاز بالفعل في هذه الجلسة.',
      });
      return;
    }

    const prevEvals = evaluationOrders.filter((order) =>
      order.items.some((item) => item.deviceId === imei),
    );
    setPreviousEvaluations(prevEvals);
    setCurrentDevice({ id: device.id, model: device.model });
    toast({
      title: 'تم العثور على الجهاز',
      description: `جاهز لتقييم ${device.model}`,
    });
  };

  const handleAddToList = () => {
    if (!currentDevice) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى البحث عن جهاز أولاً.',
      });
      return;
    }

    const newItem: EvaluatedDevice = {
      deviceId: currentDevice.id,
      model: currentDevice.model,
      externalGrade: formState.externalGrade,
      screenGrade: formState.screenGrade,
      networkGrade: formState.networkGrade,
      finalGrade: formState.finalGrade,
      fault:
        formState.finalGrade === 'يحتاج صيانة' ||
        formState.finalGrade === 'عيب فني'
          ? formState.fault === 'أعطال أخرى'
            ? formState.customFault
            : formState.fault
          : undefined,
      damageType:
        formState.finalGrade === 'تالف'
          ? formState.damage === 'أخرى'
            ? formState.customDamage
            : formState.damage
          : undefined,
    };

    setEvaluatedItems((prev) => [...prev, newItem]);
    setCurrentDevice(null);
    setImei('');
    setFormState(initialFormState);
    setPreviousEvaluations([]);
    toast({
      title: 'تمت الإضافة',
      description: `تمت إضافة ${currentDevice.model} إلى قائمة التقييم.`,
    });
  };

  const handleRemoveItem = (deviceId: string) => {
    setEvaluatedItems((prev) =>
      prev.filter((item) => item.deviceId !== deviceId),
    );
  };

  // دوال وضع الإنشاء
  const startCreating = () => {
    resetPage();
    setIsCreating(true);
    toast({
      title: 'وضع الإنشاء',
      description: 'تم تفعيل وضع إنشاء أمر تقييم جديد',
    });
  };

  const resetPage = () => {
    setImei('');
    setCurrentDevice(null);
    setFormState(initialFormState);
    setEvaluatedItems([]);
    setGeneralNotes('');
    setPreviousEvaluations([]);
    setIsCancelAlertOpen(false);
    setLoadedOrder(null);
    setIsCreating(false); // العودة إلى وضع القراءة فقط

    // حذف المسودة عند إعادة تعيين الصفحة
    clearDraft();

    // توليد رقم تقييم جديد
    const newId = generateNewEvaluationId();
    if (newId) {
      setEvaluationId(newId);
    }
  };

  const handleCreateNew = () => {
    startCreating(); // استخدام دالة وضع الإنشاء الجديدة
  };

  const handleLoadOrder = (order: EvaluationOrder) => {
    setLoadedOrder(order);
    setEvaluationId(order.orderId);
    setEvaluatedItems(order.items);
    setGeneralNotes(order.notes || '');
    setImei('');
    setCurrentDevice(null);
    setFormState(initialFormState);
    setPreviousEvaluations([]);
    setIsLoadOrderDialogOpen(false);
    setIsCreating(false); // تعطيل وضع الإنشاء عند تحميل أمر موجود
    toast({
      title: 'تم التحميل',
      description: `تم تحميل الأمر ${order.orderId}`,
    });
  };

  const handleDeleteOrder = () => {
    if (orderToDelete) {
      try {
        const relationCheck = store.checkEvaluationOrderRelations(orderToDelete.id);
        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setOrderToDelete(null);
          return;
        }

        deleteEvaluationOrder(orderToDelete.id);
        toast({
          variant: 'destructive',
          title: 'تم الحذف',
          description: `تم حذف الأمر ${orderToDelete.orderId}`,
        });
        resetPage();
        setOrderToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setOrderToDelete(null);
      }
    }
  };

  const handleSaveEvaluation = () => {
    if (evaluatedItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'القائمة فارغة',
        description: 'يرجى تقييم جهاز واحد على الأقل.',
      });
      return;
    }

    // دالة تنظيف البيانات من null bytes
    const sanitizeString = (str: any): string => {
      if (!str || typeof str !== 'string') return str || '';
      return str.replace(/\0/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim();
    };

    // تنظيف البيانات قبل الإرسال
    const cleanedItems = evaluatedItems.map(item => ({
      ...item,
      deviceId: sanitizeString(item.deviceId),
      model: sanitizeString(item.model),
      externalGrade: sanitizeString(item.externalGrade),
      screenGrade: sanitizeString(item.screenGrade),
      networkGrade: sanitizeString(item.networkGrade),
      finalGrade: sanitizeString(item.finalGrade),
      fault: item.fault ? sanitizeString(item.fault) : undefined,
      damageType: item.damageType ? sanitizeString(item.damageType) : undefined,
    }));

    const orderData = {
      orderId: sanitizeString(evaluationId),
      employeeName: sanitizeString(currentUser?.name || 'مدير النظام'),
      date: new Date().toISOString(),
      items: cleanedItems,
      notes: sanitizeString(generalNotes),
    };

    if (loadedOrder) {
      updateEvaluationOrder({
        ...orderData,
        id: loadedOrder.id,
        createdAt: loadedOrder.createdAt // الحفاظ على التاريخ الأصلي
      });
      toast({
        title: 'تم التحديث',
        description: `تم تحديث أمر التقييم ${loadedOrder.orderId} بنجاح.`,
      });
    } else {
      addEvaluationOrder(orderData);
      toast({
        title: 'تم الحفظ',
        description: `تم حفظ أمر التقييم ${evaluationId} بنجاح.`,
      });
    }

    resetPage();
  };

  const handleFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let addedCount = 0;
    let invalidCount = 0;
    let duplicateCount = 0;
    const newItems: EvaluatedDevice[] = [];
    const existingDeviceIds = new Set(
      evaluatedItems.map((item) => item.deviceId),
    );

    for (const imei of lines) {
      if (existingDeviceIds.has(imei)) {
        duplicateCount++;
        continue;
      }

      const device = devices.find((d) => d.id === imei);
      if (!device || device.status === 'مباع') {
        invalidCount++;
        continue;
      }

      const newItem: EvaluatedDevice = {
        deviceId: device.id,
        model: device.model,
        externalGrade: 'بدون',
        screenGrade: 'بدون',
        networkGrade: 'مفتوح رسمي',
        finalGrade: 'جاهز للبيع',
      };
      newItems.push(newItem);
      existingDeviceIds.add(imei);
      addedCount++;
    }

    if (newItems.length > 0) {
      setEvaluatedItems((prev) => [...prev, ...newItems]);
    }

    toast({
      title: 'اكتمل الاستيراد',
      description: `تمت إضافة ${addedCount} جهازًا. تم تخطي ${invalidCount} جهازًا (غير صالح) و ${duplicateCount} جهازًا (مكرر).`,
    });

    if (event.target) event.target.value = '';
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc.setFontSize(16);
      doc.text(settings.companyName, 190, 15, { align: 'right' });
      doc.setFontSize(10);
      doc.text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5);
      doc.line(15, 35, 195, 35);
    };

    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc.setFontSize(8);
      doc.text(
        `صفحة ${data.pageNumber} من ${pageCount - 1}`,
        data.settings.margin.left,
        doc.internal.pageSize.height - 10,
      );
      if (settings.reportFooter) {
        doc.text(
          settings.reportFooter,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' },
        );
      }
    };

    return { addHeader, addFooter };
  };

  const handleExport = async (action: 'print' | 'download') => {
    if (evaluatedItems.length === 0) return;
    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);

    addHeader();

    doc.setFontSize(18);
    doc.text('تقرير الفحص والتقييم', 190, 45, { align: 'right' });

    doc.setFontSize(12);
    doc.text(`رقم الأمر: ${evaluationId}`, 190, 52, { align: 'right' });
    doc.text(`التاريخ: ${new Date().toLocaleDateString('ar-EG')}`, 190, 59, {
      align: 'right',
    });

    autoTable(doc, {
      startY: 65,
      head: [['الوصف', 'التقييم النهائي', 'الموديل', 'الرقم التسلسلي']],
      body: evaluatedItems.map((item) => [
        item.fault || item.damageType || 'N/A',
        item.finalGrade,
        item.model,
        item.deviceId,
      ]),
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      didDrawPage: addFooter,
      theme: 'grid',
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(`${evaluationId}.pdf`);
    }
  };

  const handleExportExcel = async () => {
    if (evaluatedItems.length === 0) return;
    const XLSX = await import('xlsx');
    const worksheet = XLSX.utils.json_to_sheet(
      evaluatedItems.map((item) => ({
        'رقم الأمر': evaluationId,
        'الرقم التسلسلي': item.deviceId,
        الموديل: item.model,
        'التقييم الخارجي': item.externalGrade,
        'تقييم الشاشة': item.screenGrade,
        الشبكة: item.networkGrade,
        'التقييم النهائي': item.finalGrade,
        'العطل/التلف': item.fault || item.damageType,
      })),
    );
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير التقييم');
    XLSX.writeFile(workbook, `Evaluation-${evaluationId}.xlsx`);
  };

  return (
    <div className="grading-page">
      {/* رأس الصفحة المحسن */}
      <div className="header-card mb-6">
        <div className="p-6">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                <span className="text-xl font-bold">📋</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                  {loadedOrder
                    ? `تعديل الأمر ${loadedOrder.orderId}`
                    : 'صفحة الفحص والتقييم'}
                </h1>
                <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                  نظام تقييم الأجهزة المتقدم مع إدارة شاملة للبيانات
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* زر الوضع الليلي */}
              <DarkModeToggle
                size="md"
                variant="outline"
                className="enhanced-button"
              />

              {/* الأزرار الأساسية */}
              <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsLoadOrderDialogOpen(true)}
                className="enhanced-button border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400 transition-all duration-200"
              >
                <FolderOpen className="ml-2 h-4 w-4" /> تحميل أمر سابق
              </Button>

              {/* زر عرض مسودة */}
              <Button
                variant="outline"
                onClick={loadDraft}
                disabled={!hasSavedDraft}
                className={`enhanced-button ${
                  hasSavedDraft
                    ? 'border-amber-300 text-amber-600 hover:bg-amber-50 hover:border-amber-400'
                    : 'border-gray-300 text-gray-400 bg-gray-100'
                } transition-all duration-200`}
              >
                <FileDown className="ml-2 h-4 w-4" />
                📄 عرض مسودة
                {hasSavedDraft && (
                  <span className="enhanced-badge bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs mr-2">
                    ✅ متوفرة
                  </span>
                )}
              </Button>

              {canCreate && (
                <Button
                  onClick={handleCreateNew}
                  className="enhanced-button bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
                >
                  <PlusCircle className="ml-2 h-4 w-4" /> إنشاء أمر جديد
                </Button>
              )}
              {canDelete && (
                <Button
                  variant="destructive"
                  onClick={() => setOrderToDelete(loadedOrder)}
                  disabled={!loadedOrder}
                  className="enhanced-button bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:from-gray-300 disabled:to-gray-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none"
                >
                  <Trash className="ml-2 h-4 w-4" /> حذف الأمر
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* رسالة توضيحية في وضع القراءة */}
      {!isCreating && !loadedOrder && canCreate && (
        <div className="info-message animate-fade-in-up mb-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 text-white rounded-xl flex items-center justify-center mr-4 enhanced-icon">
              💡
            </div>
            <div>
              <div className="font-semibold text-lg text-purple-800 dark:text-purple-200">ابدأ بإنشاء أمر تقييم جديد</div>
              <div className="text-sm text-purple-600 dark:text-purple-300 mt-1">اضغط على "إنشاء أمر جديد" لتفعيل وضع الإنشاء وبدء عملية التقييم</div>
            </div>
          </div>
        </div>
      )}

      <Card className="enhanced-grading-card info-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 py-4">
          <CardTitle className="text-lg text-blue-800 dark:text-blue-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">1</div>
            <div>
              <div className="font-bold">معلومات أمر التقييم الأساسية</div>
              <div className="text-xs text-blue-600 dark:text-blue-300 font-normal mt-1">البيانات الأساسية لأمر التقييم الحالي</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
          <div className="space-y-2">
            <Label htmlFor="eval_order_id_input" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              📋 رقم الأمر
            </Label>
            <Input
              id="eval_order_id_input"
              value={evaluationId}
              disabled
              className="enhanced-input h-10 text-sm font-mono"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="eval_employee_input" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              👤 اسم المستخدم
            </Label>
            <Input
              id="eval_employee_input"
              value={currentUser?.name || 'مدير النظام'}
              disabled
              className="enhanced-input h-10 text-sm"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="eval_date_field" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              📅 التاريخ والوقت
            </Label>
            <Input
              id="eval_date_field"
              type="datetime-local"
              defaultValue={new Date().toISOString().slice(0, 16)}
              className="enhanced-input h-10 text-sm font-mono"
              style={{ direction: 'ltr' }}
              disabled
            />
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-grading-card device-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/20 dark:to-amber-950/20 py-4">
          <CardTitle className="text-lg text-orange-800 dark:text-orange-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-amber-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">2</div>
            <div>
              <div className="font-bold">إدخال الرقم التسلسلي (IMEI) وتفاصيل الجهاز</div>
              <div className="text-xs text-orange-600 dark:text-orange-300 font-normal mt-1">البحث عن الجهاز وعرض تفاصيله الأساسية</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 p-6">
          <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3 mb-3">
              <Label htmlFor="imei-input" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                📱 إدخال IMEI:
              </Label>
              <div className={`character-counter ${
                imei.length === 15 ? 'valid' : imei.length > 0 ? 'partial' : ''
              }`}>
                <span className="inline-flex items-center">
                  {imei.length === 15 && <span className="mr-1">✓</span>}
                  {imei.length}/15 رقم
                </span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Input
                id="imei-input"
                placeholder="أدخل 15 رقمًا للبحث عن الجهاز..."
                value={imei}
                onChange={(e) => setImei(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleImeiSearch()}
                disabled={!!currentDevice || (!canCreate || (!isCreating && !loadedOrder))}
                className={`enhanced-input font-mono transition-all duration-300 h-12 text-sm flex-1 ${
                  imei.length === 15
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20 shadow-md ring-2 ring-green-200 dark:ring-green-800 text-green-900 dark:text-green-100'
                    : imei.length > 0
                    ? 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20 shadow-sm ring-1 ring-yellow-200 dark:ring-yellow-800 text-yellow-900 dark:text-yellow-100'
                    : 'hover:border-blue-300 focus:ring-2 focus:ring-blue-200 dark:focus:ring-blue-800'
                }`}
              />
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileImport}
                accept=".txt"
              />
              <Button
                size="sm"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                title="استيراد من ملف نصي"
                disabled={!canCreate || (!isCreating && !loadedOrder)}
                className="enhanced-button border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400 transition-all duration-200 h-12 px-4"
              >
                <Upload className="h-4 w-4 mr-2" />
                استيراد
              </Button>
              <Button
                onClick={handleImeiSearch}
                disabled={!!currentDevice || (!canCreate || (!isCreating && !loadedOrder))}
                className="enhanced-button bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-12 px-6"
              >
                🔍 بحث
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setImei('');
                  setCurrentDevice(null);
                  setPreviousEvaluations([]);
                }}
                disabled={!currentDevice || (!canCreate || (!isCreating && !loadedOrder))}
                className="enhanced-button border-orange-300 text-orange-600 hover:bg-orange-50 hover:border-orange-400 transition-all duration-200 h-12 px-4"
              >
                🔄 تغيير
              </Button>
            </div>
          </div>
          {currentDevice && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-xl border border-green-200 dark:border-green-800 animate-slide-in-right">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-xl flex items-center justify-center mr-3">
                  ✅
                </div>
                <div>
                  <h3 className="font-bold text-green-800 dark:text-green-200">تم العثور على الجهاز</h3>
                  <p className="text-sm text-green-600 dark:text-green-300">البيانات الأساسية للجهاز المحدد</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-green-100 dark:border-green-800">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">📱 الرقم التسلسلي:</span>
                  <div className="imei-display mt-2" dir="ltr">{currentDevice.id}</div>
                </div>
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-green-100 dark:border-green-800">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">📋 الموديل:</span>
                  <div className="mt-2 font-semibold text-gray-800 dark:text-gray-200">{currentDevice.model}</div>
                </div>
              </div>
              {previousEvaluations.length > 0 && (
                <div className="md:col-span-2 mt-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-3 flex items-center">
                      📊 سجل التقييمات السابقة
                    </h4>
                    <div className="space-y-2">
                      {previousEvaluations.map((order) => (
                        <div key={order.id} className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-blue-100 dark:border-blue-800 flex items-center justify-between">
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300 rounded-lg flex items-center justify-center text-sm font-bold">
                              📋
                            </div>
                            <div>
                              <div className="font-medium text-gray-800 dark:text-gray-200">{order.orderId}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                {new Date(order.date).toLocaleDateString('ar-EG')}
                              </div>
                            </div>
                          </div>
                          <Badge className={`enhanced-badge ${
                            order.items.find((i) => i.deviceId === imei)?.finalGrade === 'جاهز للبيع' ? 'badge-ready' :
                            order.items.find((i) => i.deviceId === imei)?.finalGrade === 'يحتاج صيانة' ? 'badge-maintenance' :
                            order.items.find((i) => i.deviceId === imei)?.finalGrade === 'عيب فني' ? 'badge-defective' : 'badge-damaged'
                          }`}>
                            {order.items.find((i) => i.deviceId === imei)?.finalGrade}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {currentDevice && (
        <Card className="enhanced-grading-card grading-section animate-fade-in-up">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 py-4">
            <CardTitle className="text-lg text-green-800 dark:text-green-200 flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">3</div>
              <div>
                <div className="font-bold">نظام التقييم الفني (Grading System)</div>
                <div className="text-xs text-green-600 dark:text-green-300 font-normal mt-1">تقييم شامل لحالة الجهاز الفنية والخارجية</div>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 p-6">
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                🏷️ التقييم الخارجي
              </Label>
              <Select
                dir="rtl"
                value={formState.externalGrade}
                onValueChange={(v: EvaluationGrade) =>
                  setFormState((s) => ({ ...s, externalGrade: v }))
                }
                disabled={!canCreate || (!isCreating && !loadedOrder)}
              >
                <SelectTrigger className="enhanced-input h-12 text-sm">
                  <SelectValue placeholder="اختر التقييم..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="بدون">بدون</SelectItem>
                  <SelectItem value="A++">A++ - ممتاز جداً</SelectItem>
                  <SelectItem value="A">A - ممتاز</SelectItem>
                  <SelectItem value="B">B - جيد جداً</SelectItem>
                  <SelectItem value="C">C - جيد</SelectItem>
                  <SelectItem value="D">D - مقبول</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                📱 تقييم الشاشة
              </Label>
              <Select
                dir="rtl"
                value={formState.screenGrade}
                onValueChange={(v: ScreenGrade) =>
                  setFormState((s) => ({ ...s, screenGrade: v }))
                }
                disabled={!canCreate || (!isCreating && !loadedOrder)}
              >
                <SelectTrigger className="enhanced-input h-12 text-sm">
                  <SelectValue placeholder="اختر تقييم الشاشة..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="بدون">بدون</SelectItem>
                  <SelectItem value="A+">A+ - ممتاز</SelectItem>
                  <SelectItem value="M1">M1 - خدوش طفيفة</SelectItem>
                  <SelectItem value="M2">M2 - خدوش متوسطة</SelectItem>
                  <SelectItem value="M3">M3 - خدوش واضحة</SelectItem>
                  <SelectItem value="M4">M4 - خدوش كثيرة</SelectItem>
                  <SelectItem value="N1">N1 - نقطة صغيرة</SelectItem>
                  <SelectItem value="N2">N2 - نقاط متعددة</SelectItem>
                  <SelectItem value="F1">F1 - كسر صغير</SelectItem>
                  <SelectItem value="F2">F2 - كسر متوسط</SelectItem>
                  <SelectItem value="F3">F3 - كسر كبير</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                📶 الشبكة
              </Label>
              <Select
                dir="rtl"
                value={formState.networkGrade}
                onValueChange={(v: NetworkGrade) =>
                  setFormState((s) => ({ ...s, networkGrade: v }))
                }
                disabled={!canCreate || (!isCreating && !loadedOrder)}
              >
                <SelectTrigger className="enhanced-input h-12 text-sm">
                  <SelectValue placeholder="اختر حالة الشبكة..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="مفتوح رسمي">🔓 مفتوح رسمي</SelectItem>
                  <SelectItem value="مغلق">🔒 مغلق</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                ⭐ التقييم النهائي
              </Label>
              <Select
                dir="rtl"
                value={formState.finalGrade}
                onValueChange={(v: FinalGrade) =>
                  setFormState((s) => ({
                    ...s,
                    finalGrade: v,
                    fault: '',
                    customFault: '',
                    damage: '',
                    customDamage: '',
                  }))
                }
                disabled={!canCreate || (!isCreating && !loadedOrder)}
              >
                <SelectTrigger className="enhanced-input h-12 text-sm">
                  <SelectValue placeholder="اختر التقييم النهائي..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="جاهز للبيع">✅ جاهز للبيع</SelectItem>
                  <SelectItem value="يحتاج صيانة">🔧 يحتاج صيانة</SelectItem>
                  <SelectItem value="عيب فني">⚠️ عيب فني</SelectItem>
                  <SelectItem value="تالف">❌ تالف</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {(formState.finalGrade === 'يحتاج صيانة' ||
              formState.finalGrade === 'عيب فني') && (
              <>
                <div className="space-y-3 md:col-span-2">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                    🔧 تحديد نوع العطل
                  </Label>
                  <Select
                    dir="rtl"
                    value={formState.fault}
                    onValueChange={(v: FaultType) =>
                      setFormState((s) => ({ ...s, fault: v }))
                    }
                    disabled={!canCreate || (!isCreating && !loadedOrder)}
                  >
                    <SelectTrigger className="enhanced-input h-12 text-sm">
                      <SelectValue placeholder="اختر نوع العطل..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="شاشة">📱 شاشة</SelectItem>
                      <SelectItem value="بطارية">🔋 بطارية</SelectItem>
                      <SelectItem value="منفذ شحن">🔌 منفذ شحن</SelectItem>
                      <SelectItem value="كاميرا">📷 كاميرا</SelectItem>
                      <SelectItem value="صوت">🔊 صوت</SelectItem>
                      <SelectItem value="لمس">👆 لمس</SelectItem>
                      <SelectItem value="حساس">📡 حساس</SelectItem>
                      <SelectItem value="هزاز">📳 هزاز</SelectItem>
                      <SelectItem value="وايفاي">📶 وايفاي</SelectItem>
                      <SelectItem value="ذاكره">💾 ذاكره</SelectItem>
                      <SelectItem value="بطاقة sim">📋 بطاقة SIM</SelectItem>
                      <SelectItem value="أعطال أخرى">❓ أعطال أخرى</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {formState.fault === 'أعطال أخرى' && (
                  <div className="space-y-3 md:col-span-2">
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      📝 وصف العطل المخصص
                    </Label>
                    <Input
                      placeholder="اكتب وصف مفصل للعطل..."
                      value={formState.customFault}
                      onChange={(e) =>
                        setFormState((s) => ({
                          ...s,
                          customFault: e.target.value,
                        }))
                      }
                      className="enhanced-input h-12 text-sm"
                      disabled={!canCreate || (!isCreating && !loadedOrder)}
                    />
                  </div>
                )}
              </>
            )}

            {formState.finalGrade === 'تالف' && (
              <>
                <div className="space-y-3 md:col-span-2">
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                    ❌ تحديد نوع التلف
                  </Label>
                  <Select
                    dir="rtl"
                    value={formState.damage}
                    onValueChange={(v: DamageType) =>
                      setFormState((s) => ({ ...s, damage: v }))
                    }
                    disabled={!canCreate || (!isCreating && !loadedOrder)}
                  >
                    <SelectTrigger className="enhanced-input h-12 text-sm">
                      <SelectValue placeholder="اختر نوع التلف..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="شاشة">📱 شاشة محطمة</SelectItem>
                      <SelectItem value="ماذر بورد">🔧 ماذر بورد</SelectItem>
                      <SelectItem value="الغرق">💧 الغرق</SelectItem>
                      <SelectItem value="أخرى">❓ أخرى</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {formState.damage === 'أخرى' && (
                  <div className="space-y-3 md:col-span-2">
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      📝 وصف التلف المخصص
                    </Label>
                    <Input
                      placeholder="اكتب وصف مفصل للتلف..."
                      value={formState.customDamage}
                      onChange={(e) =>
                        setFormState((s) => ({
                          ...s,
                          customDamage: e.target.value,
                        }))
                      }
                      className="enhanced-input h-12 text-sm"
                      disabled={!canCreate || (!isCreating && !loadedOrder)}
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
          <CardFooter className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 p-6">
            <Button
              onClick={handleAddToList}
              className="enhanced-button bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 w-full h-12"
              disabled={!canCreate || (!isCreating && !loadedOrder)}
            >
              <PlusCircle className="ml-2 h-5 w-5" /> ✅ إضافة للقائمة
            </Button>
          </CardFooter>
        </Card>
      )}

      <Card className="enhanced-grading-card list-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 py-4">
          <CardTitle className="text-lg text-purple-800 dark:text-purple-200 flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">4</div>
              <div>
                <div className="font-bold">الأجهزة المضافة في الأمر الحالي</div>
                <div className="text-xs text-purple-600 dark:text-purple-300 font-normal mt-1">قائمة الأجهزة التي تم تقييمها في هذا الأمر</div>
              </div>
            </div>
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="enhanced-badge bg-gradient-to-r from-purple-500 to-violet-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                📊 {evaluatedItems.length} جهاز
              </span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="enhanced-table enhanced-scroll-area max-h-96 overflow-y-auto">
            <Table className="w-full">
              <TableHeader className="enhanced-table-header sticky top-0 z-10">
                <TableRow className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/30 dark:to-violet-950/30">
                  <TableHead className="enhanced-table-cell text-center font-bold text-purple-800 dark:text-purple-200 py-3 text-sm w-16">
                    <div className="flex items-center justify-center">
                      <span className="w-6 h-6 bg-purple-500 text-white rounded-lg flex items-center justify-center text-xs font-bold">#</span>
                    </div>
                  </TableHead>
                  <TableHead className="enhanced-table-cell text-right font-semibold text-purple-800 dark:text-purple-200 py-3 text-sm">
                    📱 الرقم التسلسلي
                  </TableHead>
                  <TableHead className="enhanced-table-cell text-right font-semibold text-purple-800 dark:text-purple-200 py-3 text-sm">
                    📋 الموديل
                  </TableHead>
                  <TableHead className="enhanced-table-cell text-right font-semibold text-purple-800 dark:text-purple-200 py-3 text-sm">
                    🏷️ التقييم الخارجي
                  </TableHead>
                  <TableHead className="enhanced-table-cell text-right font-semibold text-purple-800 dark:text-purple-200 py-3 text-sm">
                    ⭐ التقييم النهائي
                  </TableHead>
                  <TableHead className="enhanced-table-cell text-right font-semibold text-purple-800 dark:text-purple-200 py-3 text-sm">
                    📝 العطل/الوصف
                  </TableHead>
                  <TableHead className="enhanced-table-cell text-center font-semibold text-purple-800 dark:text-purple-200 py-3 text-sm">
                    ⚙️ إجراء
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {evaluatedItems.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-32">
                      <div className="empty-state">
                        <div className="empty-state-icon">📱</div>
                        <div className="empty-state-title">لا توجد أجهزة مضافة</div>
                        <div className="empty-state-description">
                          ابدأ بإضافة الأجهزة للتقييم باستخدام نظام البحث أعلاه
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  evaluatedItems.map((item, index) => (
                    <TableRow
                      key={item.deviceId}
                      className={`enhanced-table-row animate-fade-in-up ${
                        index % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50/50 dark:bg-gray-800/50'
                      }`}
                    >
                      <TableCell className="enhanced-table-cell text-center py-3 text-sm w-16">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 text-white rounded-lg flex items-center justify-center text-xs font-bold">
                          {index + 1}
                        </div>
                      </TableCell>
                      <TableCell className="enhanced-table-cell text-right font-mono text-blue-700 dark:text-blue-300 py-3 text-sm" dir="ltr">
                        <div className="imei-display text-xs">
                          {item.deviceId}
                        </div>
                      </TableCell>
                      <TableCell className="enhanced-table-cell text-right py-3 text-sm text-gray-800 dark:text-gray-200">
                        <div className="font-medium">{item.model}</div>
                      </TableCell>
                      <TableCell className="enhanced-table-cell text-right py-3 text-sm">
                        <Badge className="enhanced-badge bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          {item.externalGrade}
                        </Badge>
                      </TableCell>
                      <TableCell className="enhanced-table-cell text-right py-3">
                        <Badge className={`enhanced-badge ${
                          item.finalGrade === 'جاهز للبيع' ? 'badge-ready' :
                          item.finalGrade === 'يحتاج صيانة' ? 'badge-maintenance' :
                          item.finalGrade === 'عيب فني' ? 'badge-defective' : 'badge-damaged'
                        }`}>
                          {item.finalGrade === 'جاهز للبيع' ? '✅' :
                           item.finalGrade === 'يحتاج صيانة' ? '🔧' :
                           item.finalGrade === 'عيب فني' ? '⚠️' : '❌'} {item.finalGrade}
                        </Badge>
                      </TableCell>
                      <TableCell className="enhanced-table-cell text-right py-3 text-sm text-gray-800 dark:text-gray-200">
                        <div className="max-w-32 truncate" title={item.fault || item.damageType || 'لا يوجد'}>
                          {item.fault || item.damageType || 'لا يوجد'}
                        </div>
                      </TableCell>
                      <TableCell className="enhanced-table-cell text-center py-3">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="enhanced-button text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-200 rounded-full h-8 w-8"
                          onClick={() => handleRemoveItem(item.deviceId)}
                          title="حذف من القائمة"
                          disabled={!canCreate || (!isCreating && !loadedOrder)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-grading-card notes-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-950/20 dark:to-cyan-950/20 py-4">
          <CardTitle className="text-lg text-teal-800 dark:text-teal-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-teal-500 to-cyan-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">5</div>
            <div>
              <div className="font-bold">ملاحظات عامة</div>
              <div className="text-xs text-teal-600 dark:text-teal-300 font-normal mt-1">إضافة ملاحظات وتعليقات حول عملية التقييم</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
              📝 الملاحظات والتعليقات
            </Label>
            <Textarea
              placeholder="أضف أي ملاحظات إضافية حول عملية التقييم، حالة الأجهزة، أو أي معلومات مهمة أخرى..."
              value={generalNotes}
              onChange={(e) => setGeneralNotes(e.target.value)}
              className="enhanced-input min-h-24 text-sm resize-none"
              disabled={!canCreate || (!isCreating && !loadedOrder)}
            />
            <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
              💡 نصيحة: استخدم هذا المجال لتسجيل أي معلومات إضافية قد تكون مفيدة للمراجعة المستقبلية
            </div>
          </div>
        </CardContent>
      </Card>

      {/* أزرار الإجراءات */}
      <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
        <div className="flex flex-wrap justify-center gap-3">
          <Button
            variant="destructive"
            onClick={() => setIsCancelAlertOpen(true)}
            className="enhanced-button bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-12 px-6"
          >
            <X className="ml-2 h-5 w-5" /> ❌ إلغاء الأمر
          </Button>

          <Button
            variant="outline"
            onClick={() => handleExport('print')}
            disabled={evaluatedItems.length === 0}
            className="enhanced-button border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200 h-12 px-6"
          >
            <Printer className="ml-2 h-5 w-5" /> 🖨️ طباعة التقرير
          </Button>

          <Button
            variant="outline"
            onClick={() => handleExport('download')}
            disabled={evaluatedItems.length === 0}
            className="enhanced-button border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 transition-all duration-200 h-12 px-6"
          >
            <FileDown className="ml-2 h-5 w-5" /> 📄 تصدير PDF
          </Button>

          <Button
            variant="outline"
            onClick={handleExportExcel}
            disabled={evaluatedItems.length === 0}
            className="enhanced-button border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400 transition-all duration-200 h-12 px-6"
          >
            <FileSpreadsheet className="ml-2 h-5 w-5" /> 📊 تصدير Excel
          </Button>

          {((canCreate && isCreating) || (canEdit && loadedOrder)) && (
            <Button
              onClick={handleSaveEvaluation}
              disabled={evaluatedItems.length === 0}
              className="enhanced-button bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-12 px-8 font-bold"
            >
              <Save className="ml-2 h-5 w-5" />
              {loadedOrder ? '💾 تحديث الأمر' : '✅ حفظ التقييم'}
            </Button>
          )}

          {/* زر حفظ مسودة - يظهر فقط للأوامر الجديدة وليس المحملة */}
          {((canCreate && isCreating) || (canEdit && loadedOrder)) && !loadedOrder && (
            <Button
              variant="outline"
              onClick={saveDraft}
              disabled={evaluatedItems.length === 0 && !imei}
              className="enhanced-button border-amber-300 text-amber-600 hover:bg-amber-50 hover:border-amber-400 transition-all duration-200 h-12 px-6"
            >
              <Save className="ml-2 h-5 w-5" />
              💾 حفظ مسودة
            </Button>
          )}
        </div>

        {evaluatedItems.length === 0 && (
          <div className="text-center mt-4">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              💡 قم بإضافة الأجهزة أولاً لتفعيل خيارات التصدير والحفظ
            </p>
          </div>
        )}
      </div>
    </div>

      <AlertDialog open={isCancelAlertOpen} onOpenChange={setIsCancelAlertOpen}>
        <AlertDialogContent className="enhanced-dialog">
          <AlertDialogHeader className="enhanced-dialog-header p-6">
            <AlertDialogTitle className="text-xl font-bold text-red-800 dark:text-red-200 flex items-center">
              <div className="w-10 h-10 bg-red-500 text-white rounded-xl flex items-center justify-center mr-3">
                ⚠️
              </div>
              هل أنت متأكد من الإلغاء؟
            </AlertDialogTitle>
          </AlertDialogHeader>
          <div className="p-6">
            <AlertDialogDescription className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <p className="font-medium text-red-800 dark:text-red-200 mb-2">تحذير مهم:</p>
                <p>سيتم فقدان جميع البيانات غير المحفوظة في أمر التقييم الحالي، بما في ذلك:</p>
                <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                  <li>الأجهزة المضافة للتقييم</li>
                  <li>درجات التقييم المدخلة</li>
                  <li>الملاحظات العامة</li>
                </ul>
              </div>
            </AlertDialogDescription>
          </div>
          <AlertDialogFooter className="p-6 bg-gray-50 dark:bg-gray-800">
            <AlertDialogCancel className="enhanced-button">
              🔙 تراجع
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={resetPage}
              className="enhanced-button bg-red-500 hover:bg-red-600 text-white"
            >
              ❌ متابعة الإلغاء
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog
        open={isLoadOrderDialogOpen}
        onOpenChange={setIsLoadOrderDialogOpen}
      >
        <DialogContent className="enhanced-dialog sm:max-w-5xl max-h-[85vh]">
          <DialogHeader className="enhanced-dialog-header p-6">
            <DialogTitle className="text-xl font-bold text-green-800 dark:text-green-200 flex items-center">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-xl flex items-center justify-center mr-3">
                📂
              </div>
              <div>
                <div>تحميل أمر تقييم سابق</div>
                <div className="text-sm text-green-600 dark:text-green-300 font-normal mt-1">
                  اختر أمر تقييم من القائمة لتحميل بياناته أو تعديله
                </div>
              </div>
            </DialogTitle>
          </DialogHeader>

          {/* Container with fixed height and scroll */}
          <div className="p-6">
            <div className="enhanced-table enhanced-scroll-area h-[450px] overflow-y-auto">
              <Table className="w-full">
                <TableHeader className="enhanced-table-header sticky top-0 z-10">
                  <TableRow className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30">
                    <TableHead className="enhanced-table-cell text-center font-bold text-green-800 dark:text-green-200 py-3 text-sm w-16">
                      <div className="flex items-center justify-center">
                        <span className="w-6 h-6 bg-green-500 text-white rounded-lg flex items-center justify-center text-xs font-bold">#</span>
                      </div>
                    </TableHead>
                    <TableHead className="enhanced-table-cell text-right font-semibold text-green-800 dark:text-green-200 py-3 text-sm">
                      📋 رقم الأمر
                    </TableHead>
                    <TableHead className="enhanced-table-cell text-right font-semibold text-green-800 dark:text-green-200 py-3 text-sm">
                      📅 التاريخ والوقت
                    </TableHead>
                    <TableHead className="enhanced-table-cell text-center font-semibold text-green-800 dark:text-green-200 py-3 text-sm">
                      📱 عدد الأجهزة
                    </TableHead>
                    <TableHead className="enhanced-table-cell text-center font-semibold text-green-800 dark:text-green-200 py-3 text-sm">
                      ⚙️ إجراء
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {evaluationOrders.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-32">
                        <div className="empty-state">
                          <div className="empty-state-icon">📋</div>
                          <div className="empty-state-title">لا توجد أوامر تقييم سابقة</div>
                          <div className="empty-state-description">
                            لم يتم إنشاء أي أوامر تقييم من قبل
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    evaluationOrders.map((order, index) => (
                      <TableRow
                        key={order.id}
                        className="enhanced-table-row animate-fade-in-up"
                      >
                        <TableCell className="enhanced-table-cell text-center py-3 text-sm w-16">
                          <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-lg flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </div>
                        </TableCell>
                        <TableCell className="enhanced-table-cell text-right font-mono text-blue-700 dark:text-blue-300 font-medium py-3 text-sm">
                          <div className="font-bold">{order.orderId}</div>
                        </TableCell>
                        <TableCell className="enhanced-table-cell text-right font-mono text-gray-600 dark:text-gray-300 py-3 text-sm" style={{ direction: 'ltr' }}>
                          <div className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                            {formatDateTime(order.date)}
                          </div>
                        </TableCell>
                        <TableCell className="enhanced-table-cell text-center py-3 text-sm">
                          <Badge className="enhanced-badge bg-gradient-to-r from-green-500 to-emerald-600 text-white">
                            📱 {order.items.length} جهاز
                          </Badge>
                        </TableCell>
                        <TableCell className="enhanced-table-cell text-center py-3">
                          <Button
                            size="sm"
                            onClick={() => handleLoadOrder(order)}
                            className="enhanced-button bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-sm hover:shadow-md transition-all duration-200 h-9 px-4 text-sm"
                          >
                            📂 تحميل
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
          <DialogFooter className="p-6 bg-gray-50 dark:bg-gray-800">
            <DialogClose asChild>
              <Button variant="outline" className="enhanced-button">
                🔙 إغلاق
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog
        open={!!orderToDelete}
        onOpenChange={() => setOrderToDelete(null)}
      >
        <AlertDialogContent className="enhanced-dialog">
          <AlertDialogHeader className="enhanced-dialog-header p-6">
            <AlertDialogTitle className="text-xl font-bold text-red-800 dark:text-red-200 flex items-center">
              <div className="w-10 h-10 bg-red-500 text-white rounded-xl flex items-center justify-center mr-3">
                🗑️
              </div>
              <div>
                <div>هل أنت متأكد من الحذف؟</div>
                <div className="text-sm text-red-600 dark:text-red-300 font-normal mt-1">
                  {orderToDelete?.orderId}
                </div>
              </div>
            </AlertDialogTitle>
          </AlertDialogHeader>
          <div className="p-6">
            <AlertDialogDescription className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                <p className="font-medium text-red-800 dark:text-red-200 mb-2">تحذير مهم:</p>
                <p>هذا الإجراء لا يمكن التراجع عنه. سيتم حذف أمر التقييم بشكل دائم مع جميع البيانات المرتبطة به:</p>
                <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                  <li>جميع الأجهزة المقيمة في هذا الأمر</li>
                  <li>درجات التقييم والملاحظات</li>
                  <li>سجل التاريخ والمستخدم</li>
                </ul>
              </div>
            </AlertDialogDescription>
          </div>
          <AlertDialogFooter className="p-6 bg-gray-50 dark:bg-gray-800">
            <AlertDialogCancel className="enhanced-button">
              🔙 تراجع
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteOrder}
              className="enhanced-button bg-red-500 hover:bg-red-600 text-white"
            >
              🗑️ متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
