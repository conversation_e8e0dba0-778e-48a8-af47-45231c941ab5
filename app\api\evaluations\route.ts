import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const evaluations = await prisma.evaluationOrder.findMany({
      include: {
        items: true
      },
      orderBy: { id: 'desc' }
    });

    return NextResponse.json(evaluations);
  } catch (error) {
    console.error('Failed to fetch evaluations:', error);
    return NextResponse.json({ error: 'Failed to fetch evaluations' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newEvaluation = await request.json();

    // دالة تنظيف البيانات من null bytes
    const sanitizeString = (str: any): string | null => {
      if (!str || typeof str !== 'string') return str;
      // إزالة null bytes وأحرف التحكم الأخرى
      return str.replace(/\0/g, '').replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '').trim() || null;
    };

    // تنظيف البيانات الأساسية
    const cleanedEvaluation = {
      ...newEvaluation,
      orderId: sanitizeString(newEvaluation.orderId),
      employeeName: sanitizeString(newEvaluation.employeeName),
      notes: sanitizeString(newEvaluation.notes),
      warehouseName: sanitizeString(newEvaluation.warehouseName),
      acknowledgedBy: sanitizeString(newEvaluation.acknowledgedBy),
      items: newEvaluation.items ? newEvaluation.items.map((item: any) => ({
        ...item,
        deviceId: sanitizeString(item.deviceId),
        model: sanitizeString(item.model),
        externalGrade: sanitizeString(item.externalGrade),
        screenGrade: sanitizeString(item.screenGrade),
        networkGrade: sanitizeString(item.networkGrade),
        finalGrade: sanitizeString(item.finalGrade),
        fault: sanitizeString(item.fault),
        damageType: sanitizeString(item.damageType)
      })) : []
    };

    // Basic validation
    if (!cleanedEvaluation.orderId || !cleanedEvaluation.employeeName) {
      return NextResponse.json(
        { error: 'Order ID and employee name are required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if evaluation order already exists
      const existingEvaluation = await tx.evaluationOrder.findUnique({
        where: { orderId: newEvaluation.orderId }
      });

      if (existingEvaluation) {
        throw new Error('Evaluation order with this ID already exists');
      }

      // Create the evaluation order in the database
      const evaluation = await tx.evaluationOrder.create({
        data: {
          orderId: newEvaluation.orderId,
          employeeName: newEvaluation.employeeName || authResult.user!.username,
          date: newEvaluation.date || new Date().toISOString(),
          notes: newEvaluation.notes || null,
          status: newEvaluation.status || 'معلق',
          acknowledgedBy: newEvaluation.acknowledgedBy || null,
          acknowledgedDate: newEvaluation.acknowledgedDate || null,
          warehouseName: newEvaluation.warehouseName || null,
        }
      });

      
      // Create evaluationOrder items
      if (newEvaluation.items && Array.isArray(newEvaluation.items)) {
        for (const item of newEvaluation.items) {
          await tx.evaluationOrderItem.create({
            data: {
              evaluationOrderId: evaluation.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              externalGrade: item.externalGrade || '',
              screenGrade: item.screenGrade || '',
              networkGrade: item.networkGrade || '',
              finalGrade: item.finalGrade || '',
              fault: item.fault || null,
              damageType: item.damageType || null
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created evaluation order: ${evaluation.orderId}`,
        tableName: 'evaluationOrder',
        recordId: evaluation.id.toString()
      });

      return evaluation;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to create evaluation:', error);

    if (error instanceof Error && error.message === 'Evaluation order with this ID already exists') {
      return NextResponse.json({ error: 'Evaluation order with this ID already exists' }, { status: 400 });
    }

    return NextResponse.json({ error: 'Failed to create evaluation' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedEvaluation = await request.json();

    if (!updatedEvaluation.id) {
      return NextResponse.json(
        { error: 'Evaluation ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if evaluation exists
      const existingEvaluation = await tx.evaluationOrder.findUnique({
        where: { id: updatedEvaluation.id }
      });

      if (!existingEvaluation) {
        throw new Error('Evaluation not found');
      }

      // Update the evaluation
      const evaluation = await tx.evaluationOrder.update({
        where: { id: updatedEvaluation.id },
        data: {
          orderId: updatedEvaluation.orderId || existingEvaluation.orderId,
          employeeName: updatedEvaluation.employeeName || existingEvaluation.employeeName,
          date: updatedEvaluation.date || existingEvaluation.date,
          notes: updatedEvaluation.notes !== undefined ? updatedEvaluation.notes : existingEvaluation.notes,
          status: updatedEvaluation.status || existingEvaluation.status,
          acknowledgedBy: updatedEvaluation.acknowledgedBy !== undefined ? updatedEvaluation.acknowledgedBy : existingEvaluation.acknowledgedBy,
          acknowledgedDate: updatedEvaluation.acknowledgedDate !== undefined ? updatedEvaluation.acknowledgedDate : existingEvaluation.acknowledgedDate,
          warehouseName: updatedEvaluation.warehouseName !== undefined ? updatedEvaluation.warehouseName : existingEvaluation.warehouseName,
        }
      });

      // تحديث العناصر - حذف القديمة وإنشاء الجديدة
      if (updatedEvaluation.items && Array.isArray(updatedEvaluation.items)) {
        // حذف العناصر القديمة
        await tx.evaluationOrderItem.deleteMany({
          where: { evaluationOrderId: updatedEvaluation.id }
        });

        // إنشاء العناصر الجديدة
        for (const item of updatedEvaluation.items) {
          await tx.evaluationOrderItem.create({
            data: {
              evaluationOrderId: evaluation.id,
              deviceId: item.deviceId || '',
              model: item.model || '',
              externalGrade: item.externalGrade || '',
              screenGrade: item.screenGrade || '',
              networkGrade: item.networkGrade || '',
              finalGrade: item.finalGrade || '',
              fault: item.fault || null,
              damageType: item.damageType || null
            }
          });
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated evaluation order: ${evaluation.orderId}`,
        tableName: 'evaluationOrder',
        recordId: evaluation.id.toString()
      });

      return evaluation;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update evaluation:', error);

    if (error instanceof Error && error.message === 'Evaluation not found') {
      return NextResponse.json({ error: 'Evaluation not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update evaluation' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Evaluation ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if evaluation exists
      const existingEvaluation = await tx.evaluationOrder.findUnique({
        where: { id: parseInt(id) }
      });

      if (!existingEvaluation) {
        throw new Error('Evaluation not found');
      }

      // Delete the evaluation
      await tx.evaluationOrder.delete({
        where: { id: parseInt(id) }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted evaluation order: ${existingEvaluation.orderId}`,
        tableName: 'evaluationOrder',
        recordId: id.toString()
      });

      return { message: 'Evaluation deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete evaluation:', error);

    if (error instanceof Error && error.message === 'Evaluation not found') {
      return NextResponse.json({ error: 'Evaluation not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete evaluation' }, { status: 500 });
  }
}
