
// Required API endpoints:
// - /api/devices
// - /api/warehouses
// - /api/sales
// - /api/returns
'use client';

import { useState, useEffect, useMemo } from 'react';
import {
  format,
  subDays,
  startOfDay,
  endOfDay,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
} from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';
import type { Device, DeviceStatus, SystemSettings, Warehouse, Sale, Return, Manufacturer, DeviceModel } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Eye,
  Filter,
  ChevronsUpDown,
  Check,
  Printer,
  FileDown,
  FileSpreadsheet,
} from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';

type ModelSummary = {
  model: string;
  total: number;
  available: number;
  maintenance: number;
  inRepair: number;
  sold: number;
  defective: number;
  damaged: number;
  // New fields for detailed evaluations
  defectiveDevices: Device[];
  damagedDevices: Device[];
  maintenanceDevices: Device[];
  inRepairDevices: Device[];
  soldDevices: Device[];
  availableDevices: Device[];
  // New fields for device condition
  newDevices: Device[];
  usedDevices: Device[];
};

export default function InventoryPage() {
  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [devices, setDevices] = useState<Device[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [returns, setReturns] = useState<Return[]>([]);
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([]);
  const [deviceModels, setDeviceModels] = useState<DeviceModel[]>([]);
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    id: 1,
    logoUrl: '',
    companyNameAr: 'نظام إدارة الأجهزة',
    companyNameEn: 'Device Management System',
    addressAr: '',
    addressEn: '',
    phone: '',
    email: '',
    website: '',
    footerTextAr: '',
    footerTextEn: '',
    updatedAt: new Date(),
    createdAt: new Date()
  });

  const { toast } = useToast();

  // API functions
  const fetchDevices = async () => {
    try {
      const response = await fetch('/api/devices-simple');
      if (!response.ok) {
        console.warn('Devices API not available, using empty data');
        setDevices([]);
        return;
      }
      const data = await response.json();
      console.log('تم تحميل الأجهزة:', data.length, 'جهاز');
      setDevices(data);
    } catch (error) {
      console.error('خطأ في تحميل الأجهزة:', error);
      // استخدام بيانات فارغة بدلاً من إظهار خطأ للمستخدم
      setDevices([]);
      toast({
        variant: 'default',
        title: 'تنبيه',
        description: 'لم يتم تحميل بيانات الأجهزة - سيتم استخدام بيانات فارغة'
      });
    }
  };

  const fetchWarehouses = async () => {
    try {
      const response = await fetch('/api/warehouses-simple');
      if (!response.ok) throw new Error('فشل في تحميل المخازن');
      const data = await response.json();
      setWarehouses(data);
    } catch (error) {
      console.error('خطأ في تحميل المخازن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل المخازن'
      });
    }
  };

  const fetchSales = async () => {
    try {
      const response = await fetch('/api/sales-simple');
      if (!response.ok) {
        console.warn('Sales API not available, using empty data');
        setSales([]);
        return;
      }
      const data = await response.json();
      console.log('تم تحميل المبيعات:', data.length, 'مبيعة');
      setSales(data);
    } catch (error) {
      console.error('خطأ في تحميل المبيعات:', error);
      // استخدام بيانات فارغة بدلاً من إظهار خطأ للمستخدم
      setSales([]);
      toast({
        variant: 'default',
        title: 'تنبيه',
        description: 'لم يتم تحميل بيانات المبيعات - سيتم استخدام بيانات فارغة'
      });
    }
  };

  const fetchReturns = async () => {
    try {
      const response = await fetch('/api/returns-simple');
      if (!response.ok) {
        console.warn('Returns API not available, using empty data');
        setReturns([]);
        return;
      }
      const data = await response.json();
      console.log('تم تحميل المرتجعات:', data.length, 'مرتجع');
      setReturns(data);
    } catch (error) {
      console.error('خطأ في تحميل المرتجعات:', error);
      // استخدام بيانات فارغة بدلاً من إظهار خطأ للمستخدم
      setReturns([]);
      toast({
        variant: 'default',
        title: 'تنبيه',
        description: 'لم يتم تحميل بيانات المرتجعات - سيتم استخدام بيانات فارغة'
      });
    }
  };

  const fetchManufacturers = async () => {
    try {
      // استخدام بيانات ثابتة للمصنعين حتى يتم إنشاء API
      const manufacturersData = [
        { id: 1, name: 'Apple' },
        { id: 2, name: 'Samsung' },
        { id: 3, name: 'Google' },
        { id: 4, name: 'OnePlus' },
        { id: 5, name: 'Xiaomi' }
      ];
      setManufacturers(manufacturersData);
    } catch (error) {
      console.error('خطأ في تحميل المصنعين:', error);
    }
  };

  const fetchDeviceModels = async () => {
    try {
      const response = await fetch('/api/device-models-simple');
      if (!response.ok) {
        console.warn('Device models API not available, using empty data');
        setDeviceModels([]);
        return;
      }
      const data = await response.json();
      console.log('تم تحميل موديلات الأجهزة:', data.length, 'موديل');
      setDeviceModels(data);
    } catch (error) {
      console.error('خطأ في تحميل موديلات الأجهزة:', error);
      // استخدام بيانات فارغة بدلاً من إظهار خطأ للمستخدم
      setDeviceModels([]);
      toast({
        variant: 'default',
        title: 'تنبيه',
        description: 'لم يتم تحميل موديلات الأجهزة - سيتم استخدام بيانات فارغة'
      });
    }
  };

  const fetchSystemSettings = async () => {
    try {
      const response = await fetch('/api/settings-simple');
      if (!response.ok) {
        console.warn('Settings API not available, using default settings');
        return; // Keep the default settings from useState
      }
      const data = await response.json();
      setSystemSettings(data);
    } catch (error) {
      console.error('خطأ في تحميل إعدادات النظام:', error);
      // استخدام الإعدادات الافتراضية في حالة الخطأ (المحددة في useState)
      toast({
        variant: 'default',
        title: 'تنبيه',
        description: 'لم يتم تحميل إعدادات النظام - سيتم استخدام الإعدادات الافتراضية'
      });
    }
  };

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/suppliers-simple');
      if (!response.ok) {
        console.warn('Suppliers API not available, using empty data');
        setSuppliers([]);
        return;
      }
      const data = await response.json();
      setSuppliers(data);
    } catch (error) {
      console.error('خطأ في تحميل الموردين:', error);
      // استخدام بيانات فارغة بدلاً من إظهار خطأ للمستخدم
      setSuppliers([]);
      toast({
        variant: 'default',
        title: 'تنبيه',
        description: 'لم يتم تحميل بيانات الموردين - سيتم استخدام بيانات فارغة'
      });
    }
  };

  const fetchAllData = async () => {
    setIsLoading(true);
    await Promise.all([
      fetchDevices(),
      fetchWarehouses(),
      fetchSales(),
      fetchReturns(),
      fetchSuppliers(),
      fetchManufacturers(),
      fetchDeviceModels(),
      fetchSystemSettings()
    ]);
    setIsLoading(false);
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [detailsModel, setDetailsModel] = useState<ModelSummary | null>(null);
  
  // إضافة حالة لنافذة عرض جميع الأجهزة
  const [isAllDevicesModalOpen, setIsAllDevicesModalOpen] = useState(false);
  const [allDevicesSearchQuery, setAllDevicesSearchQuery] = useState("");
  const [allDevicesStatusFilter, setAllDevicesStatusFilter] = useState("all");
  
  // إضافة متغيرات لنافذة تتبع الجهاز
  const [isDeviceTrackingModalOpen, setIsDeviceTrackingModalOpen] = useState(false);
  const [deviceTrackingDetails, setDeviceTrackingDetails] = useState<Device | null>(null);
  
  // إضافة متغيرات التنقل بين الصفحات
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // عدد العناصر في كل صفحة

  const [isDeviceDetailsModalOpen, setIsDeviceDetailsModalOpen] =
    useState(false);
  const [deviceDetails, setDeviceDetails] = useState<{
    title: string;
    devices: Device[];
  } | null>(null);

  // Filtering states
  const [selectedModel, setSelectedModel] = useState('all');
  const [isModelSearchOpen, setIsModelSearchOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [showTotalOnly, setShowTotalOnly] = useState(false); // إضافة متغير حالة جديد للإجمالي فقط
  const [warehouseFilter, setWarehouseFilter] = useState('');
  const [supplierFilter, setSupplierFilter] = useState('');
  const [manufacturerFilter, setManufacturerFilter] = useState('');
  const [returnedOnlyFilter, setReturnedOnlyFilter] = useState(false);
  const [dateRange, setDateRange] = useState<string>('all');
  const [customDate, setCustomDate] = useState<Date | undefined>(undefined);

  const returnedDeviceIds = useMemo(
    () => new Set(returns.flatMap((r) => (Array.isArray(r.items) ? r.items : []).map((i) => i.deviceId))),
    [returns]
  );

  const manufacturerNames = useMemo(
    () =>
      manufacturers.reduce(
        (acc, m) => {
          acc[m.id] = m.name;
          return acc;
        },
        {} as { [key: number]: string },
      ),
    [manufacturers]
    );

  const modelOptions = useMemo(() => {
    return [...new Set(devices.map((d) => d.model))].sort();
  }, [devices]);

  const baseFilteredDevices = useMemo(() => {
    let filtered = devices.filter((device) => {
      if (statusFilter.length > 0 && !statusFilter.includes(device.status)) return false;
      if (warehouseFilter && device.warehouseId?.toString() !== warehouseFilter) return false;
      if (supplierFilter && device.supplierId?.toString() !== supplierFilter) return false;
      if (manufacturerFilter) {
        const manuName = manufacturerNames[parseInt(manufacturerFilter, 10)];
        if (
          !manuName ||
          !device.model.toLowerCase().startsWith(manuName.toLowerCase())
        ) return false;
      }
      if (returnedOnlyFilter && !returnedDeviceIds.has(device.id)) return false;
      return true;
    });

    // Apply date range filter
    if (dateRange !== 'all') {
      const now = new Date();
      let startDate: Date | null = null;
      let endDate: Date | null = null;

      switch (dateRange) {
        case 'day':
          startDate = startOfDay(now);
          endDate = endOfDay(now);
          break;
        case 'threeDays':
          startDate = startOfDay(subDays(now, 2));
          endDate = endOfDay(now);
          break;
        case 'week':
          startDate = startOfDay(subDays(now, 6));
          endDate = endOfDay(now);
          break;
        case 'month':
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
          break;
        case 'threeMonths':
          startDate = startOfMonth(subDays(now, 90));
          endDate = endOfMonth(now);
          break;
        case 'sixMonths':
          startDate = startOfMonth(subDays(now, 180));
          endDate = endOfMonth(now);
          break;
        case 'year':
          startDate = startOfYear(now);
          endDate = endOfYear(now);
          break;
        case 'custom':
          if (customDate) {
            startDate = startOfDay(customDate);
            endDate = endOfDay(customDate);
          }
          break;
      }

      if (startDate && endDate) {
        filtered = filtered.filter((device) => {
          const deviceDate = new Date(device.dateAdded);
          return deviceDate >= startDate! && deviceDate <= endDate!;
        });
      }
    }

    return filtered;
  }, [
    devices,
    statusFilter,
    warehouseFilter,
    supplierFilter,
    manufacturerFilter,
    returnedOnlyFilter,
    returnedDeviceIds,
    manufacturerNames,
    dateRange,
    customDate,
  ]);

  const summaryData = useMemo((): ModelSummary[] => {
    const summary = baseFilteredDevices.reduce(
      (acc, device) => {
        if (!acc[device.model]) {
          acc[device.model] = {
            model: device.model,
            total: 0,
            available: 0,
            maintenance: 0,
            inRepair: 0,
            sold: 0,
            defective: 0,
            damaged: 0,
            defectiveDevices: [],
            damagedDevices: [],
            maintenanceDevices: [],
            inRepairDevices: [],
            soldDevices: [],
            availableDevices: [],
            newDevices: [],
            usedDevices: [],
          };
        }
        const modelSummary = acc[device.model];
        modelSummary.total++;
        
        // Process device condition (new or used)
        if (device.condition === 'جديد') {
          modelSummary.newDevices.push(device);
        } else if (device.condition === 'مستخدم') {
          modelSummary.usedDevices.push(device);
        }
        
        // Process device status
        switch (device.status) {
          case 'متاح للبيع':
            modelSummary.available++;
            modelSummary.availableDevices.push(device);
            break;
          case 'بانتظار إرسال للصيانة':
          case 'تحتاج صيانة':
          case 'بانتظار استلام في الصيانة':
          case 'قيد الإصلاح':
          case 'بانتظار تسليم من الصيانة':
          case 'بانتظار قطع غيار':
          case 'مراجعة الطلب من الإدارة':
            modelSummary.maintenance++;
            modelSummary.maintenanceDevices.push(device);
            break;
          case 'مباع':
            modelSummary.sold++;
            modelSummary.soldDevices.push(device);
            break;
          case 'معيب':
            modelSummary.defective++;
            modelSummary.defectiveDevices.push(device);
            break;
          case 'تالف':
            modelSummary.damaged++;
            modelSummary.damagedDevices.push(device);
            break;
        }
        return acc;
      },
      {} as Record<string, ModelSummary>
    );
    return Object.values(summary).sort((a, b) =>
      a.model.localeCompare(b.model)
    );
  }, [baseFilteredDevices]);

  const filteredSummaryData = useMemo(() => {
    if (selectedModel === 'all') {
      return summaryData;
    }
    return summaryData.filter((item) => item.model === selectedModel);
  }, [summaryData, selectedModel]);

  const handleStatusFilterChange = (status: string) => {
    // التعامل مع خيارات خاصة
    if (status === 'كل التقييمات') {
      setStatusFilter([
        'متاح للبيع',
        'تحتاج صيانة',
        'قيد الإصلاح',
        'مرسل للمخزن',
        'مباع',
        'معيب',
        'تالف',
      ]);
      setShowTotalOnly(false);
      setCurrentPage(1);
      return;
    }
    
    if (status === 'الإجمالي فقط') {
      // عند تفعيل "الإجمالي فقط"، نتأكد من إلغاء كل الخيارات الأخرى
      if (!showTotalOnly) {
        setStatusFilter([]);
      }
      setShowTotalOnly(!showTotalOnly);
      setCurrentPage(1);
      return;
    }
    
    // التعامل مع التقييمات العادية
    setStatusFilter((prev) =>
      prev.includes(status)
        ? prev.filter((s) => s !== status)
        : [...prev, status]
    );
    setCurrentPage(1); // إعادة تعيين رقم الصفحة عند تغيير الفلاتر
  };

  const clearFilters = () => {
    setSelectedModel('all');
    setStatusFilter([]);
    setShowTotalOnly(false);
    setWarehouseFilter('');
    setSupplierFilter('');
    setManufacturerFilter('');
    setReturnedOnlyFilter(false);
    setCurrentPage(1); // إعادة تعيين رقم الصفحة عند مسح الفلاتر
  };
  
  // إضافة وظيفة تحديث المخزون
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchAllData();
      toast({
        title: "تم التحديث بنجاح",
        description: "تم تحديث بيانات المخزون",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "خطأ في التحديث",
        description: "الرجاء المحاولة مرة أخرى"
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleOpenDetailsModal = (modelSummary: ModelSummary) => {
    setDetailsModel(modelSummary);
    setIsDetailsModalOpen(true);
  };

  const handleOpenDeviceDetailsModal = (title: string, devices: Device[]) => {
    setDeviceDetails({ title, devices });
    setIsDeviceDetailsModalOpen(true);
  };
  
  // إضافة وظيفة فتح نافذة تتبع الجهاز
  const handleOpenDeviceTrackingModal = (device: Device) => {
    setDeviceTrackingDetails(device);
    setIsDeviceTrackingModalOpen(true);
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(settings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc
        .setFontSize(8)
        .text(
          `صفحة ${data.pageNumber} من ${pageCount - 1}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
    );
      if (settings.reportFooter) {
        doc.text(
          settings.reportFooter,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' }
    );
      }
    };
    return { addHeader, addFooter };
  };

  const handleExport = (format: 'pdf' | 'excel') => {
    if (filteredSummaryData.length === 0) {
      toast({ variant: 'destructive', title: 'لا توجد بيانات للتصدير' });
      return;
    }

    const title = `تقرير المخزون ${selectedModel === 'all' ? 'الإجمالي' : selectedModel}`;
    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
      addHeader();
      doc.setFontSize(18).text(title, 190, 45, { align: 'right' });

      const head = [
        [
          'تالف',
          'معيب',
          'مباع',
          'قيد الإصلاح',
          'صيانة',
          'المتاح',
          'الإجمالي',
          'الموديل',
        ],
      ];
      const body = filteredSummaryData.map((item) => [
        item.damaged,
        item.defective,
        item.sold,
        item.inRepair,
        item.maintenance,
        item.available,
        item.total,
        item.model,
      ]);

      autoTable(doc, {
        head: head,
        body: body,
        startY: 55,
        styles: { font: 'Helvetica', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
        columnStyles: {
          0: { halign: 'center' },
          1: { halign: 'center' },
          2: { halign: 'center' },
          3: { halign: 'center' },
          4: { halign: 'center' },
          5: { halign: 'center' },
          6: { halign: 'center' },
        },
        didDrawPage: addFooter,
      });
      doc.save(`inventory_report_${new Date().toISOString().slice(0, 10)}.pdf`);
    } else {
      const xlsxData = filteredSummaryData.map((item) => ({
        الموديل: item.model,
        الإجمالي: item.total,
        المتاح: item.available,
        صيانة: item.maintenance,
        'قيد الإصلاح': item.inRepair,
        مباع: item.sold,
        معيب: item.defective,
        تالف: item.damaged,
      }));
      const worksheet = XLSX.utils.json_to_sheet(xlsxData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير المخزون');
      XLSX.writeFile(
        workbook,
        `inventory_report_${new Date().toISOString().slice(0, 10)}.xlsx`
    );
    }
  };

  const handleDetailsModalExport = (action: 'print' | 'download') => {
    if (!detailsModel) return;

    const doc = new jsPDF();
    doc.setR2L(true);
    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();

    const title = `تقرير تفصيلي لموديل ${detailsModel.model}`;
    doc.setFontSize(18).text(title, 190, 45, { align: 'right' });

    const summaryBody = [
      ['الإجمالي', detailsModel.total],
      ['متاح للبيع', detailsModel.available],
      ['يحتاج صيانة', detailsModel.maintenance],
      ['قيد الإصلاح', detailsModel.inRepair],
      ['مباع', detailsModel.sold],
      ['معيب', detailsModel.defective],
      ['تالف', detailsModel.damaged],
    ];

    autoTable(doc, {
      startY: 55,
      head: [['الحالة', 'العدد']],
      body: summaryBody,
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      columnStyles: { 1: { halign: 'center' } },
      didDrawPage: addFooter,
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(
        `inventory_details_${detailsModel.model.replace(/\s+/g, '_')}.pdf`
    );
    }
  };
  
  // التعامل مع نافذة عرض كل الأجهزة
  const filteredAllDevices = useMemo(() => {
    let filtered = baseFilteredDevices;
    
    // تطبيق البحث
    if (allDevicesSearchQuery) {
      const query = allDevicesSearchQuery.toLowerCase();
      filtered = filtered.filter(device => 
        device.id.toLowerCase().includes(query) || 
        device.model.toLowerCase().includes(query)
      );
    }
    
    // تطبيق فلتر الحالة
    if (allDevicesStatusFilter !== "all") {
      filtered = filtered.filter(device => device.status === allDevicesStatusFilter);
    }
    
    return filtered;
  }, [baseFilteredDevices, allDevicesSearchQuery, allDevicesStatusFilter]);

  const totalInventory = useMemo(() => {
    // ✅ حساب إجمالي المخزون المتوفر فقط (بدون الأجهزة المباعة)
    return filteredSummaryData.reduce((sum, item) => sum + (item.total - item.sold), 0);
  }, [filteredSummaryData]);
  
  // حساب عدد الصفحات وعناصر الصفحة الحالية
  const totalPages = useMemo(() => {
    return Math.max(1, Math.ceil(filteredSummaryData.length / itemsPerPage));
  }, [filteredSummaryData, itemsPerPage]);
  
  // الموديلات المعروضة في الصفحة الحالية
  const currentPageItems = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredSummaryData.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredSummaryData, currentPage, itemsPerPage]);

  return (
    <div className="flex flex-col gap-4">
    {/* Dashboard Summary Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-4">
      <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">إجمالي المخزون المتوفر</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalInventory}</div>
          <p className="text-xs text-muted-foreground mt-1">
            {filteredSummaryData.length} موديل مختلف (بدون المباع)
          </p>
        </CardContent>
      </Card>
      
      <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">متاح للبيع</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {filteredSummaryData.reduce((sum, item) => sum + item.available, 0)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {totalInventory > 0 ? ((filteredSummaryData.reduce((sum, item) => sum + item.available, 0) / totalInventory) * 100).toFixed(1) : 0}% من المخزون المتوفر
          </p>
        </CardContent>
      </Card>
      
      <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950 dark:to-yellow-900">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">اجمالي الاجهزة الجديدة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {filteredSummaryData.reduce((sum, item) => sum + (item.newDevices?.filter(d => d.status !== 'مباع').length || 0), 0)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {totalInventory > 0 ? ((filteredSummaryData.reduce((sum, item) => sum + (item.newDevices?.filter(d => d.status !== 'مباع').length || 0), 0) / totalInventory) * 100).toFixed(1) : 0}% من المخزون المتوفر
          </p>
        </CardContent>
      </Card>
      
      <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">اجمالي اجهزة مستخدمة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {filteredSummaryData.reduce((sum, item) => sum + (item.usedDevices?.filter(d => d.status !== 'مباع').length || 0), 0)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {totalInventory > 0 ? ((filteredSummaryData.reduce((sum, item) => sum + (item.usedDevices?.filter(d => d.status !== 'مباع').length || 0), 0) / totalInventory) * 100).toFixed(1) : 0}% من المخزون المتوفر
          </p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">الأجهزة الأخرى</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {filteredSummaryData.reduce((sum, item) => sum + item.maintenance + item.defective + item.damaged, 0)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            صيانة ومعيب وتالف
          </p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950 dark:to-gray-900">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">إجمالي الأجهزة المباعة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {filteredSummaryData.reduce((sum, item) => sum + item.sold, 0)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {filteredSummaryData.reduce((sum, item) => sum + item.total, 0) > 0 ? ((filteredSummaryData.reduce((sum, item) => sum + item.sold, 0) / filteredSummaryData.reduce((sum, item) => sum + item.total, 0)) * 100).toFixed(1) : 0}% من إجمالي الأجهزة
          </p>
        </CardContent>
      </Card>
    </div>

    <div className="flex items-center justify-between">
      <h2 className="text-xl font-semibold">
        تقرير المخزون المتوفر ({totalInventory})
      </h2>
      <div className="flex gap-2">
        <Button variant="outline" onClick={() => handleExport('pdf')}>
          <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
        </Button>
        <Button variant="outline" onClick={() => handleExport('excel')}>
          <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
        </Button>
      </div>
    </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>تصفية متقدمة</CardTitle>
            <Button variant="ghost" size="sm" onClick={clearFilters} className="h-8 px-2 lg:px-3">
              مسح كل الفلاتر
            </Button>
          </div>
          
          {/* Refresh Button */}
          <div className="mt-2 flex justify-end">
            <Button 
              variant="outline" 
              size="sm" 
              className="gap-1" 
              onClick={handleRefresh} 
              disabled={isRefreshing}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="16" height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                className={`ml-1 ${isRefreshing ? 'animate-spin' : ''}`}
              >
                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                <path d="M21 3v5h-5" />
                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                <path d="M8 16H3v5" />
              </svg>
              {isRefreshing ? 'جاري التحديث...' : 'تحديث'}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pt-0">
          <div className="space-y-2">
            <Label>بحث بالموديل</Label>
            <Popover
              open={isModelSearchOpen}
              onOpenChange={setIsModelSearchOpen}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isModelSearchOpen}
                  className="w-full justify-between"
                >
                  {selectedModel === 'all' ? 'كل الموديلات' : selectedModel}
                  <ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                <Command>
                  <CommandInput placeholder="ابحث عن موديل..." />
                  <CommandList>
                    <CommandEmpty>لا يوجد موديل بهذا الاسم.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        key="all"
                        value="كل الموديلات"
                        onSelect={() => {
                          setSelectedModel('all');
                          setIsModelSearchOpen(false);
                        }}
                      >
                        <Check
                          className={cn(
                            'ml-2 h-4 w-4',
                            selectedModel === 'all'
                              ? 'opacity-100'
                              : 'opacity-0',
                          )}
                        />
                        كل الموديلات
                      </CommandItem>
                      {modelOptions.map((model) => (
                        <CommandItem
                          key={model}
                          value={model}
                          onSelect={() => {
                            setSelectedModel(model);
                            setIsModelSearchOpen(false);
                          }}
                        >
                          <Check
                            className={cn(
                              'ml-2 h-4 w-4',
                              selectedModel === model
                                ? 'opacity-100'
                                : 'opacity-0',
                            )}
                          />
                          {model}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label>التقييم</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between text-right"
                >
                  <span>
                    {statusFilter.length > 0
                      ? `${statusFilter.length} تقييمات محددة`
                      : 'كل التقييمات'}
                  </span>
                  <Filter className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end">
                <DropdownMenuLabel>تصفية حسب التقييم</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {/* خيارات خاصة */}
                <DropdownMenuCheckboxItem
                  key="كل التقييمات"
                  checked={statusFilter.length >= 7}
                  onCheckedChange={() => handleStatusFilterChange('كل التقييمات')}
                  className="justify-end border-b mb-1 pb-1"
                >
                  كل التقييمات
                </DropdownMenuCheckboxItem>
                <DropdownMenuCheckboxItem
                  key="الإجمالي فقط"
                  checked={showTotalOnly}
                  onCheckedChange={() => handleStatusFilterChange('الإجمالي فقط')}
                  className="justify-end border-b mb-1 pb-1"
                >
                  الإجمالي فقط
                </DropdownMenuCheckboxItem>
                
                {/* التقييمات العادية */}
                {[
                  'متاح للبيع',
                  'تحتاج صيانة',
                  'قيد الإصلاح',
                  'مرسل للمخزن',
                  'مباع',
                  'معيب',
                  'تالف',
                  'مرتجعات فقط',
                  'اجهزة جديدة',
                  'اجهزه مستخدمة',
                ].map((status) => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={statusFilter.includes(status)}
                    onCheckedChange={() => handleStatusFilterChange(status)}
                    className="justify-end"
                  >
                    {status}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="space-y-2">
            <Label htmlFor="warehouse-filter">المخزن</Label>
            <Select
              dir="rtl"
              value={warehouseFilter}
              onValueChange={(value) =>
                setWarehouseFilter(value === 'all' ? '' : value)
              }
            >
              <SelectTrigger id="warehouse-filter">
                <SelectValue placeholder="كل المخازن" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل المخازن</SelectItem>
                {warehouses.map((w) => (
                  <SelectItem key={w.id} value={w.id.toString()}>
                    {w.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="supplier-filter">المورد</Label>
            <Select
              dir="rtl"
              value={supplierFilter}
              onValueChange={(value) =>
                setSupplierFilter(value === 'all' ? '' : value)
              }
            >
              <SelectTrigger id="supplier-filter">
                <SelectValue placeholder="كل الموردين" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الموردين</SelectItem>
                {suppliers.map((s) => (
                  <SelectItem key={s.id} value={s.id.toString()}>
                    {s.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="manufacturer-filter">الشركة المصنعة</Label>
            <Select
              dir="rtl"
              value={manufacturerFilter}
              onValueChange={(value) =>
                setManufacturerFilter(value === 'all' ? '' : value)
              }
            >
              <SelectTrigger id="manufacturer-filter">
                <SelectValue placeholder="كل الشركات" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الشركات</SelectItem>
                {manufacturers.map((m) => (
                  <SelectItem key={m.id} value={m.id.toString()}>
                    {m.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="date-range-filter">تصفية حسب التاريخ</Label>
            <Select
              dir="rtl"
              value={dateRange}
              onValueChange={(value) => {
                setDateRange(value);
                if (value !== 'custom') setCustomDate(undefined);
              }}
            >
              <SelectTrigger id="date-range-filter">
                <SelectValue placeholder="كل التواريخ" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل التواريخ</SelectItem>
                <SelectItem value="day">اليوم</SelectItem>
                <SelectItem value="threeDays">آخر 3 أيام</SelectItem>
                <SelectItem value="week">آخر أسبوع</SelectItem>
                <SelectItem value="month">آخر شهر</SelectItem>
                <SelectItem value="threeMonths">آخر 3 أشهر</SelectItem>
                <SelectItem value="sixMonths">آخر 6 أشهر</SelectItem>
                <SelectItem value="year">آخر سنة</SelectItem>
                <SelectItem value="custom">تاريخ مخصص</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {dateRange === 'custom' && (
            <div className="space-y-2">
              <Label htmlFor="custom-date">تاريخ محدد</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={'outline'}
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !customDate && 'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="ml-2 h-4 w-4" />
                    {customDate ? (
                      format(customDate, 'PPP')
                    ) : (
                      <span>اختر تاريخ</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={customDate}
                    onSelect={setCustomDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}
          {/* Returns Only checkbox removed as it's now in the status dropdown */}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex items-center text-sm text-muted-foreground">
            <span>الفلاتر النشطة: </span>
            <div className="flex flex-wrap gap-1 mr-2">
              {selectedModel !== 'all' && (
                <Badge variant="secondary" className="text-xs flex items-center gap-1">
                  {selectedModel}
                  <button
                    onClick={() => setSelectedModel('all')}
                    className="ml-1 rounded-full hover:bg-muted w-4 h-4 inline-flex items-center justify-center"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {statusFilter.map(status => (
                <Badge key={status} variant="secondary" className="text-xs flex items-center gap-1">
                  {status}
                  <button
                    onClick={() => handleStatusFilterChange(status)}
                    className="ml-1 rounded-full hover:bg-muted w-4 h-4 inline-flex items-center justify-center"
                  >
                    ×
                  </button>
                </Badge>
              ))}
              {warehouseFilter && (
                <Badge variant="secondary" className="text-xs flex items-center gap-1">
                  {warehouses.find(w => w.id.toString() === warehouseFilter)?.name || warehouseFilter}
                  <button
                    onClick={() => setWarehouseFilter('')}
                    className="ml-1 rounded-full hover:bg-muted w-4 h-4 inline-flex items-center justify-center"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {dateRange !== 'all' && (
                <Badge variant="secondary" className="text-xs flex items-center gap-1">
                  {dateRange === 'day' ? 'اليوم' :
                   dateRange === 'week' ? 'أسبوع' :
                   dateRange === 'month' ? 'شهر' :
                   dateRange === 'custom' ? 'تاريخ مخصص' : dateRange}
                  <button
                    onClick={() => setDateRange('all')}
                    className="ml-1 rounded-full hover:bg-muted w-4 h-4 inline-flex items-center justify-center"
                  >
                    ×
                  </button>
                </Badge>
              )}
            </div>
          </div>
          <Button variant="ghost" onClick={clearFilters} size="sm">
            مسح كل الفلاتر
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>قائمة الموديلات</CardTitle>
            <div className="flex items-center gap-2">
              <Select defaultValue="total">
                <SelectTrigger className="h-8 w-[150px]">
                  <SelectValue placeholder="ترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="model">الموديل (أبجدي)</SelectItem>
                  <SelectItem value="total">الإجمالي (تنازلي)</SelectItem>
                  <SelectItem value="available">المتاح (تنازلي)</SelectItem>
                  <SelectItem value="maintenance">الصيانة (تنازلي)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50">
                <TableHead className="w-[25%]">الشركة المصنعة</TableHead>
                <TableHead className="w-[25%]">الموديل</TableHead>
                <TableHead className="text-center">الإجمالي</TableHead>
                {statusFilter.includes('متاح للبيع') || statusFilter.length === 0 ? (
                  <TableHead className="text-center">متاح للبيع</TableHead>
                ) : null}
                {statusFilter.includes('تحتاج صيانة') || statusFilter.length === 0 ? (
                  <TableHead className="text-center">يحتاج صيانة</TableHead>
                ) : null}
                {statusFilter.includes('قيد الإصلاح') || statusFilter.length === 0 ? (
                  <TableHead className="text-center">قيد الإصلاح</TableHead>
                ) : null}
                {statusFilter.includes('معيب') || statusFilter.includes('تالف') || statusFilter.length === 0 ? (
                  <TableHead className="text-center">معيب/تالف</TableHead>
                ) : null}
                {statusFilter.includes('اجهزة جديدة') ? (
                  <TableHead className="text-center">أجهزة جديدة</TableHead>
                ) : null}
                {statusFilter.includes('اجهزه مستخدمة') ? (
                  <TableHead className="text-center">أجهزة مستخدمة</TableHead>
                ) : null}
                <TableHead className="text-center">التفاصيل</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentPageItems.length > 0 ? (
                currentPageItems.map((item) => {
                  // Extract manufacturer name from model
                  const modelParts = item.model.split(' ');
                  const manufacturer = modelParts[0];
                  const modelName = modelParts.slice(1).join(' ');
                  
                  return (
                    <TableRow key={item.model} className="hover:bg-muted/50">
                      <TableCell className="font-medium">{manufacturer}</TableCell>
                      <TableCell>{modelName}</TableCell>
                      <TableCell className="text-center font-semibold">{item.total - item.sold}</TableCell>
                      
                      {!showTotalOnly && (statusFilter.includes('متاح للبيع') || statusFilter.length === 0) ? (
                        <TableCell className="text-center">
                          <Badge variant="outline" className={`${item.available > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' : ''}`}>
                            {item.available}
                          </Badge>
                        </TableCell>
                      ) : null}
                      
                      {!showTotalOnly && (statusFilter.includes('تحتاج صيانة') || statusFilter.length === 0) ? (
                        <TableCell className="text-center">
                          <Badge variant="outline" className={`${item.maintenance > 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100' : ''}`}>
                            {item.maintenance}
                          </Badge>
                        </TableCell>
                      ) : null}
                      
                      {!showTotalOnly && (statusFilter.includes('قيد الإصلاح') || statusFilter.length === 0) ? (
                        <TableCell className="text-center">
                          <Badge variant="outline" className={`${item.inRepair > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100' : ''}`}>
                            {item.inRepair}
                          </Badge>
                        </TableCell>
                      ) : null}
                      
                      {!showTotalOnly && (statusFilter.includes('معيب') || statusFilter.includes('تالف') || statusFilter.length === 0) ? (
                        <TableCell className="text-center">
                          <Badge variant="outline" className={`${(item.defective + item.damaged) > 0 ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100' : ''}`}>
                            {item.defective + item.damaged}
                          </Badge>
                        </TableCell>
                      ) : null}
                      
                      {!showTotalOnly && statusFilter.includes('اجهزة جديدة') ? (
                        <TableCell className="text-center">
                          <Badge variant="outline" className={`${(item.newDevices?.length || 0) > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' : ''}`}>
                            {item.newDevices?.length || 0}
                          </Badge>
                        </TableCell>
                      ) : null}
                      
                      {!showTotalOnly && statusFilter.includes('اجهزه مستخدمة') ? (
                        <TableCell className="text-center">
                          <Badge variant="outline" className={`${(item.usedDevices?.length || 0) > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100' : ''}`}>
                            {item.usedDevices?.length || 0}
                          </Badge>
                        </TableCell>
                      ) : null}
                      
                      <TableCell>
                        <div className="flex justify-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleOpenDetailsModal(item)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={7} className="h-24 text-center">
                    لا توجد بيانات تطابق الفلاتر المحددة.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <CardFooter className="flex items-center justify-between py-4">
          <div className="text-sm text-muted-foreground">
            إجمالي المخزون: {filteredSummaryData.length}
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            >
              السابق
            </Button>
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(pageNum => (
              <Button 
                key={pageNum}
                variant={currentPage === pageNum ? "default" : "outline"} 
                size="sm" 
                className="w-8 p-0"
                onClick={() => setCurrentPage(pageNum)}
              >
                {pageNum}
              </Button>
            ))}
            <Button 
              variant="outline" 
              size="sm" 
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            >
              التالي
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setIsAllDevicesModalOpen(true)}
            >
              عرض الكل
            </Button>
          </div>
        </CardFooter>
      </Card>

      <Dialog open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>تفاصيل موديل: {detailsModel?.model}</DialogTitle>
            <DialogDescription>
              نظرة مفصلة على حالة الأجهزة لهذا الموديل.
            </DialogDescription>
          </DialogHeader>
          
          {/* Summary View */}
          <div className="py-4">
            {/* Summary Cards */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
              <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">الإجمالي</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">{detailsModel?.total}</div>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950 dark:to-green-900">
                <CardHeader className="pb-2 flex flex-row items-center justify-between">
                  <CardTitle className="text-sm font-medium">الأجهزة الجديدة</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0"
                    onClick={() => handleOpenDeviceDetailsModal('الأجهزة الجديدة', detailsModel?.newDevices || [])}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">{detailsModel?.newDevices?.length || 0}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {detailsModel?.newDevices?.length && detailsModel?.total ? ((detailsModel.newDevices.length / detailsModel.total) * 100).toFixed(1) : 0}% من الإجمالي
                  </div>
                </CardContent>
              </Card>
              
              <Card className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950 dark:to-yellow-900">
                <CardHeader className="pb-2 flex flex-row items-center justify-between">
                  <CardTitle className="text-sm font-medium">الأجهزة المستخدمة</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0"
                    onClick={() => handleOpenDeviceDetailsModal('الأجهزة المستخدمة', detailsModel?.usedDevices || [])}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">{detailsModel?.usedDevices?.length || 0}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {detailsModel?.usedDevices?.length && detailsModel?.total ? ((detailsModel.usedDevices.length / detailsModel.total) * 100).toFixed(1) : 0}% من الإجمالي
                  </div>
                </CardContent>
              </Card>
            </div>
            
            {/* Status Distribution Chart */}
            <div className="border rounded-lg p-4 mb-4">
              <h3 className="text-sm font-medium mb-4">توزيع حالات الأجهزة</h3>
              <div className="flex items-center h-8 rounded-md overflow-hidden">
                {detailsModel?.available ? (
                  <div
                    className="bg-green-500 h-full"
                    style={{width: `${(detailsModel.available / detailsModel.total) * 100}%`}}
                    title={`متاح للبيع: ${detailsModel.available} (${((detailsModel.available / detailsModel.total) * 100).toFixed(1)}%)`}
                  ></div>
                ) : null}
                {detailsModel?.maintenance ? (
                  <div
                    className="bg-yellow-500 h-full"
                    style={{width: `${(detailsModel.maintenance / detailsModel.total) * 100}%`}}
                    title={`يحتاج صيانة: ${detailsModel.maintenance} (${((detailsModel.maintenance / detailsModel.total) * 100).toFixed(1)}%)`}
                  ></div>
                ) : null}
                {detailsModel?.inRepair ? (
                  <div
                    className="bg-blue-500 h-full"
                    style={{width: `${(detailsModel.inRepair / detailsModel.total) * 100}%`}}
                    title={`قيد الإصلاح: ${detailsModel.inRepair} (${((detailsModel.inRepair / detailsModel.total) * 100).toFixed(1)}%)`}
                  ></div>
                ) : null}
                {detailsModel?.sold ? (
                  <div
                    className="bg-purple-500 h-full"
                    style={{width: `${(detailsModel.sold / detailsModel.total) * 100}%`}}
                    title={`مباع: ${detailsModel.sold} (${((detailsModel.sold / detailsModel.total) * 100).toFixed(1)}%)`}
                  ></div>
                ) : null}
                {detailsModel?.defective ? (
                  <div
                    className="bg-red-500 h-full"
                    style={{width: `${(detailsModel.defective / detailsModel.total) * 100}%`}}
                    title={`معيب: ${detailsModel.defective} (${((detailsModel.defective / detailsModel.total) * 100).toFixed(1)}%)`}
                  ></div>
                ) : null}
                {detailsModel?.damaged ? (
                  <div
                    className="bg-rose-700 h-full"
                    style={{width: `${(detailsModel.damaged / detailsModel.total) * 100}%`}}
                    title={`تالف: ${detailsModel.damaged} (${((detailsModel.damaged / detailsModel.total) * 100).toFixed(1)}%)`}
                  ></div>
                ) : null}
              </div>
              
              {/* Legend */}
              <div className="flex flex-wrap gap-4 mt-3">
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                  <span className="text-xs">متاح للبيع ({detailsModel?.available})</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-yellow-500 rounded-sm"></div>
                  <span className="text-xs">يحتاج صيانة ({detailsModel?.maintenance})</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-blue-500 rounded-sm"></div>
                  <span className="text-xs">قيد الإصلاح ({detailsModel?.inRepair})</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-purple-500 rounded-sm"></div>
                  <span className="text-xs">مباع ({detailsModel?.sold})</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
                  <span className="text-xs">معيب ({detailsModel?.defective})</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-rose-700 rounded-sm"></div>
                  <span className="text-xs">تالف ({detailsModel?.damaged})</span>
                </div>
              </div>
            </div>
            
            {/* Status Cards */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {/* Available Devices */}
              <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
                  <CardTitle className="text-md font-medium">
                    متاح للبيع ({detailsModel?.available})
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleOpenDeviceDetailsModal(
                        'الأجهزة المتاحة للبيع',
                        detailsModel?.availableDevices || [],
                      )
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
              </Card>

              {/* Maintenance Devices */}
              <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
                  <CardTitle className="text-md font-medium">
                    يحتاج صيانة ({detailsModel?.maintenance})
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleOpenDeviceDetailsModal(
                        'الأجهزة التي تحتاج صيانة',
                        detailsModel?.maintenanceDevices || [],
                      )
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
              </Card>

              {/* In Repair Devices */}
              <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
                  <CardTitle className="text-md font-medium">
                    قيد الإصلاح ({detailsModel?.inRepair})
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleOpenDeviceDetailsModal(
                        'الأجهزة قيد الإصلاح',
                        detailsModel?.inRepairDevices || [],
                      )
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
              </Card>

              {/* Sold Devices */}
              <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
                  <CardTitle className="text-md font-medium">
                    مباع ({detailsModel?.sold})
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleOpenDeviceDetailsModal(
                        'الأجهزة المباعة',
                        detailsModel?.soldDevices || [],
                      )
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
              </Card>

              {/* Defective Devices */}
              <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
                  <CardTitle className="text-md font-medium">
                    معيب ({detailsModel?.defective})
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleOpenDeviceDetailsModal(
                        'الأجهزة المعيبة',
                        detailsModel?.defectiveDevices || [],
                      )
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
              </Card>

              {/* Damaged Devices */}
              <Card className="w-full">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 p-3">
                  <CardTitle className="text-md font-medium">
                    تالف ({detailsModel?.damaged})
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      handleOpenDeviceDetailsModal(
                        'الأجهزة التالفة',
                        detailsModel?.damagedDevices || [],
                      )
                    }
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </CardHeader>
              </Card>
            </div>
          </div>
          <DialogFooter className="gap-2 sm:justify-start">
            <Button
              variant="outline"
              onClick={() => handleDetailsModalExport('print')}
            >
              <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
            <Button
              variant="outline"
              onClick={() => handleDetailsModalExport('download')}
            >
              <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
            </Button>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isDeviceDetailsModalOpen}
        onOpenChange={setIsDeviceDetailsModalOpen}
      >
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>{deviceDetails?.title}</DialogTitle>
            <DialogDescription>
              قائمة تفصيلية بالأجهزة في هذه الفئة.
            </DialogDescription>
          </DialogHeader>
          
          <div className="mb-4 flex items-center justify-end">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // تصدير إلى PDF
                  const doc = new jsPDF();
                  doc.setR2L(true);
                  const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
                  addHeader();
                  
                  const title = deviceDetails?.title || '';
                  doc.setFontSize(16).text(title, 190, 45, { align: 'right' });
                  
                  // تحديد العناوين بناءً على نوع الصفحة
                  let headers = ['IMEI', 'الموديل', 'الحالة'];
                  
                  if (deviceDetails?.title.includes('تحتاج صيانة') || deviceDetails?.title.includes('قيد الإصلاح')) {
                    headers.push('العطل');
                  } else if (deviceDetails?.title.includes('معيبة')) {
                    headers.push('العيب');
                  } else if (deviceDetails?.title.includes('تالفة')) {
                    headers.push('نوع التلف');
                  } else {
                    headers.push('حالة الجهاز');
                  }
                  
                  headers.push('السعة');
                  
                  // إعداد البيانات للتصدير
                  const tableData = deviceDetails?.devices.map(device => {
                    let specialColumn = '';
                       if (deviceDetails.title.includes('تحتاج صيانة') || deviceDetails.title.includes('قيد الإصلاح')) {
                    specialColumn = '-';
                  } else if (deviceDetails.title.includes('معيبة')) {
                    specialColumn = '-';
                  } else if (deviceDetails.title.includes('تالفة')) {
                    specialColumn = '-';
                  } else {
                    specialColumn = device.condition || '-';
                  }
                    
                    return [
                      device.id,
                      device.model,
                      device.status,
                      specialColumn,
                      device.storage || '-'
                    ];
                  }) || [];
                  
                  autoTable(doc, {
                    startY: 55,
                    head: [headers],
                    body: tableData,
                    styles: { font: 'Helvetica', halign: 'right' },
                    headStyles: { halign: 'center', fillColor: [44, 51, 51] },
                    didDrawPage: addFooter,
                  });
                  
                  doc.save(`${deviceDetails?.title.replace(/\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.pdf`);
                }}
              >
                <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // تصدير إلى Excel
                  const xlsxData = deviceDetails?.devices.map(device => {
                    let data: Record<string, any> = {
                      'IMEI': device.id,
                      'الموديل': device.model,
                      'الحالة': device.status,
                      'السعة': device.storage || '-',
                    };
                    
                    if (deviceDetails.title.includes('تحتاج صيانة') || deviceDetails.title.includes('قيد الإصلاح')) {
                      data['العطل'] = '-';
                    } else if (deviceDetails.title.includes('معيبة')) {
                      data['العيب'] = '-';
                    } else if (deviceDetails.title.includes('تالفة')) {
                      data['نوع التلف'] = '-';
                    } else {
                      data['حالة الجهاز'] = device.condition || '-';
                    }
                    
                    return data;
                  }) || [];
                  
                  const worksheet = XLSX.utils.json_to_sheet(xlsxData);
                  const workbook = XLSX.utils.book_new();
                  XLSX.utils.book_append_sheet(workbook, worksheet, 'الأجهزة');
                  XLSX.writeFile(workbook, `${deviceDetails?.title.replace(/\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.xlsx`);
                }}
              >
                <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
              </Button>
            </div>
          </div>
          
          <div className="max-h-[500px] overflow-y-auto">
            <Table>
              <TableHeader className="sticky top-0 bg-background">
                <TableRow>
                  <TableHead>IMEI</TableHead>
                  <TableHead>الموديل</TableHead>
                  <TableHead>الحالة</TableHead>
                  {deviceDetails?.title.includes('تحتاج صيانة') || deviceDetails?.title.includes('قيد الإصلاح') ? (
                    <TableHead>العطل</TableHead>
                  ) : deviceDetails?.title.includes('معيبة') ? (
                    <TableHead>العيب</TableHead>
                  ) : deviceDetails?.title.includes('تالفة') ? (
                    <TableHead>نوع التلف</TableHead>
                  ) : (
                    <TableHead>حالة الجهاز</TableHead>
                  )}
                  <TableHead>السعة</TableHead>
                  <TableHead className="text-center">تتبع الجهاز</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {deviceDetails?.devices.map((device) => {
                  // تحديد المحتوى الديناميكي للأعمدة بناءً على نوع الصفحة
                  let deviceInfoColumn;
                  
                  if (deviceDetails.title.includes('تحتاج صيانة') || deviceDetails.title.includes('قيد الإصلاح')) {
                    deviceInfoColumn = 'غير محدد';  // العطل
                  } else if (deviceDetails.title.includes('معيبة')) {
                    deviceInfoColumn = 'غير محدد';  // نوع العيب
                  } else if (deviceDetails.title.includes('تالفة')) {
                    deviceInfoColumn = 'غير محدد';  // نوع التلف
                  } else {
                    deviceInfoColumn = device.condition || 'غير محدد';  // حالة الجهاز (جديد/مستخدم)
                  }
                  
                  return (
                    <TableRow key={device.id} className="hover:bg-muted/50">
                      <TableCell className="font-mono">{device.id}</TableCell>
                      <TableCell>{device.model}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={
                            device.status === 'متاح للبيع' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' :
                            device.status === 'معيب' || device.status === 'تالف' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100' :
                            'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100'
                          }
                        >
                          {device.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{deviceInfoColumn}</TableCell>
                      <TableCell>{device.storage}</TableCell>
                      <TableCell className="text-center">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-8 w-8 p-0"
                          onClick={() => handleOpenDeviceTrackingModal(device)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex items-center justify-between pt-2">
            <div className="text-sm text-muted-foreground">
              إجمالي الأجهزة: {deviceDetails?.devices.length || 0}
            </div>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>

      {/* نافذة تتبع الجهاز */}
      <Dialog
        open={isDeviceTrackingModalOpen}
        onOpenChange={setIsDeviceTrackingModalOpen}
      >
        <DialogContent className="sm:max-w-4xl max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>تتبع الجهاز: {deviceTrackingDetails?.id}</DialogTitle>
            <DialogDescription>
              سجل كامل لدورة حياة الجهاز
            </DialogDescription>
          </DialogHeader>

          <div className="flex items-center gap-4 p-4 border rounded-md mb-4">
            <div className="text-4xl font-bold text-primary flex items-center justify-center bg-primary/10 w-16 h-16 rounded-full">
              {deviceTrackingDetails?.model?.charAt(0) || '?'}
            </div>
            <div>
              <h3 className="font-semibold">{deviceTrackingDetails?.model}</h3>
              <div className="text-sm text-muted-foreground">IMEI: {deviceTrackingDetails?.id}</div>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={deviceTrackingDetails?.condition === 'جديد' ? 'default' : 'secondary'}>
                  {deviceTrackingDetails?.condition || '-'}
                </Badge>
                <Badge 
                  variant="outline"
                  className={
                    deviceTrackingDetails?.status === 'متاح للبيع' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' :
                    deviceTrackingDetails?.status === 'معيب' || deviceTrackingDetails?.status === 'تالف' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100' :
                    'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100'
                  }
                >
                  {deviceTrackingDetails?.status}
                </Badge>
              </div>
            </div>
          </div>
          
          {/* مؤشر خط زمني لتتبع حياة الجهاز */}
          <div className="overflow-y-auto flex-1">
            <div className="space-y-8 relative before:absolute before:inset-0 before:right-4 before:w-0.5 before:-translate-x-1/2 before:bg-border before:h-full">
              {/* توريد الجهاز */}
              <div className="relative flex gap-6">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white shrink-0 relative z-10">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M5 8V5c0-1 1-2 2-2h10c1 0 2 1 2 2v3" /><path d="M20 10V8H4v2c0 4.4 1.6 8 4 8h.5" /><path d="M11 16h9" /><path d="M17.8 20H20v-4h-2.2" /><path d="M11 20v-4" /></svg>
                </div>
                <div className="flex flex-col gap-1 pb-8 pr-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-semibold text-md">توريد</h4>
                    <span className="text-xs text-muted-foreground">رقم التوريد: {'غير متوفر'}</span>
                  </div>
                  <p className="text-muted-foreground text-sm">تم إضافة الجهاز إلى المخزون</p>
                  <div className="bg-muted/50 p-3 rounded-md mt-2">
                    <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">تاريخ التوريد:</span>
                        <span className="font-medium mr-1">
                          {deviceTrackingDetails?.dateAdded ? new Date(deviceTrackingDetails.dateAdded).toLocaleDateString('ar-SA') : '-'}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">المورد:</span>
                        <span className="font-medium mr-1">
                          {suppliers.find(s => s.id === deviceTrackingDetails?.supplierId)?.name || '-'}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">المستخدم:</span>
                        <span className="font-medium mr-1">{'غير متوفر'}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">المخزن:</span>
                        <span className="font-medium mr-1">
                          {warehouses.find(w => w.id === deviceTrackingDetails?.warehouseId)?.name || '-'}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">السعر:</span>
                        <span className="font-medium mr-1">{deviceTrackingDetails?.price || '-'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* تقييم الجهاز */}
              <div className="relative flex gap-6">
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-yellow-500 text-white shrink-0 relative z-10">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2l2.6 7.5h7.9l-6.2 4.5 2.3 7-6.6-4.5L5.4 21l2.3-7L1.5 9.5h7.9L12 2z" /></svg>
                </div>
                <div className="flex flex-col gap-1 pb-8 pr-2">
                  <h4 className="font-semibold text-md">التقييم</h4>
                  <p className="text-muted-foreground text-sm">تم تقييم الجهاز وتحديد حالته</p>
                  <div className="bg-muted/50 p-3 rounded-md mt-2">
                    <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">حالة الجهاز:</span>
                        <span className="font-medium mr-1">{deviceTrackingDetails?.condition || '-'}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">نتيجة التقييم:</span>
                        <span className="font-medium mr-1">{deviceTrackingDetails?.status || '-'}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">تاريخ التقييم:</span>
                        <span className="font-medium mr-1">{'غير متوفر'}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">المقيِّم:</span>
                        <span className="font-medium mr-1">{'غير متوفر'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* إذا كان الجهاز احتاج صيانة، أظهر تفاصيل الصيانة */}
              {deviceTrackingDetails?.status === 'بانتظار إرسال للصيانة' || deviceTrackingDetails?.status === 'قيد الإصلاح' ? (
                <div className="relative flex gap-6">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white shrink-0 relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z" /></svg>
                  </div>
                  <div className="flex flex-col gap-1 pb-8 pr-2">
                    <h4 className="font-semibold text-md">الصيانة</h4>
                    <p className="text-muted-foreground text-sm">تم تحويل الجهاز للصيانة</p>
                    <div className="bg-muted/50 p-3 rounded-md mt-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">تاريخ الإرسال:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">العطل:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">مركز الصيانة:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">المستخدم:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}

              {/* إذا كان الجهاز تم تحويله بين المخازن */}
              {false ? (
                <div className="relative flex gap-6">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-purple-500 text-white shrink-0 relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M2 20h20" /><path d="M12 4v9.5" /><path d="m4.93 10.93 7.07 7.07 7.07-7.07" /></svg>
                  </div>
                  <div className="flex flex-col gap-1 pb-8 pr-2">
                    <h4 className="font-semibold text-md">تحويل مخزني</h4>
                    <p className="text-muted-foreground text-sm">تم نقل الجهاز بين المخازن</p>
                    <div className="bg-muted/50 p-3 rounded-md mt-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">من:</span>
                          <span className="font-medium mr-1">
                            {'غير متوفر'}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">إلى:</span>
                          <span className="font-medium mr-1">
                            {'غير متوفر'}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">التاريخ:</span>
                          <span className="font-medium mr-1">
                            {'غير متوفر'}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">المستخدم:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
              
              {/* إذا كان الجهاز مباع */}
              {deviceTrackingDetails?.status === 'مباع' ? (
                <div className="relative flex gap-6">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-500 text-white shrink-0 relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M7.5 8.5c-.2-1.4.1-2.5 1.5-3 1.4-.5 2.5.1 3 1.5.5 1.4.1 2.5-1.3 3-1.4.5-2.5.1-3-1.5z" /><path d="M11 18a2 2 0 1 0 4 0c0-.5-.08-1.66-2-3-1.92 1.34-2 2.5-2 3z" /><path d="M5 18a2 2 0 1 0 4 0c0-1.75-.5-3-2-3-1.5 0-2 1.25-2 3z" /><path d="M12 12a4 4 0 0 0 8 0 4 4 0 0 0-8 0z" /><path d="M18 12h.01" /><path d="M14 12h.01" /><path d="m9 15-2 8" /><path d="m15 15 2 8" /><path d="M12 16a5 5 0 0 1-5-5V7h10v4a5 5 0 0 1-5 5Z" /></svg>
                  </div>
                  <div className="flex flex-col gap-1 pb-8 pr-2">
                    <h4 className="font-semibold text-md">البيع</h4>
                    <p className="text-muted-foreground text-sm">تم بيع الجهاز</p>
                    <div className="bg-muted/50 p-3 rounded-md mt-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">تاريخ البيع:</span>
                          <span className="font-medium mr-1">
                            {'غير متوفر'}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">رقم الفاتورة:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">العميل:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">سعر البيع:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">المستخدم:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
              
              {/* إذا كان الجهاز تم إرجاعه */}
              {false ? (
                <div className="relative flex gap-6">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-orange-500 text-white shrink-0 relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m9 14-4-4 4-4" /><path d="M5 10h11.5a3.5 3.5 0 0 1 0 7h-1" /></svg>
                  </div>
                  <div className="flex flex-col gap-1 pb-8 pr-2">
                    <h4 className="font-semibold text-md">المرتجعات</h4>
                    <p className="text-muted-foreground text-sm">تم إرجاع الجهاز</p>
                    <div className="bg-muted/50 p-3 rounded-md mt-2">
                      <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                        <div>
                          <span className="text-muted-foreground">تاريخ الإرجاع:</span>
                          <span className="font-medium mr-1">
                            {'غير متوفر'}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">سبب الإرجاع:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">المستخدم:</span>
                          <span className="font-medium mr-1">{'غير متوفر'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : null}
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button variant="outline" size="sm" onClick={() => {
              // تصدير تقرير تتبع الجهاز
              const doc = new jsPDF();
              doc.setR2L(true);
              const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
              addHeader();
              
              doc.setFontSize(16).text(`تقرير تتبع الجهاز: ${deviceTrackingDetails?.id}`, 190, 45, { align: 'right' });
              doc.setFontSize(12).text(`الموديل: ${deviceTrackingDetails?.model}`, 190, 55, { align: 'right' });
              doc.setFontSize(12).text(`الحالة: ${deviceTrackingDetails?.status}`, 190, 65, { align: 'right' });
              
              // هنا يمكن إضافة المزيد من المعلومات التفصيلية للتقرير
              
              doc.save(`device_tracking_${deviceTrackingDetails?.id}.pdf`);
            }}>
              <FileDown className="ml-2 h-4 w-4" /> تصدير التقرير
            </Button>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* نافذة عرض جميع الموديلات بدون تقسيم صفحات */}
      <Dialog
        open={isAllDevicesModalOpen}
        onOpenChange={setIsAllDevicesModalOpen}
      >
        <DialogContent className="sm:max-w-[98vw] w-[98vw] flex flex-col">
          <DialogHeader>
            <DialogTitle>عرض جميع الموديلات</DialogTitle>
            <DialogDescription>
              عرض كامل لجميع الموديلات
            </DialogDescription>
          </DialogHeader>
          
          <div style={{ height: '500px', maxHeight: '500px', width: 'fit-content', minWidth: '100%' }} className="overflow-y-auto overflow-x-visible custom-scrollbar">
            <Table>
              <TableHeader className="sticky top-0 bg-background">
                <TableRow className="bg-muted/50">
                  <TableHead className={showTotalOnly ? "w-[30%]" : "w-[15%]"}>الشركة المصنعة</TableHead>
                  <TableHead className={showTotalOnly ? "w-[30%]" : "w-[15%]"}>الموديل</TableHead>
                  <TableHead className="text-center">الإجمالي</TableHead>
                  {!showTotalOnly && (statusFilter.includes('متاح للبيع') || statusFilter.length === 0) ? (
                    <TableHead className="text-center">متاح للبيع</TableHead>
                  ) : null}
                  {!showTotalOnly && (statusFilter.includes('تحتاج صيانة') || statusFilter.length === 0) ? (
                    <TableHead className="text-center">يحتاج صيانة</TableHead>
                  ) : null}
                  {!showTotalOnly && (statusFilter.includes('قيد الإصلاح') || statusFilter.length === 0) ? (
                    <TableHead className="text-center">قيد الإصلاح</TableHead>
                  ) : null}
                  {!showTotalOnly && (statusFilter.includes('معيب') || statusFilter.includes('تالف') || statusFilter.length === 0) ? (
                    <TableHead className="text-center">معيب/تالف</TableHead>
                  ) : null}
                  {!showTotalOnly && statusFilter.includes('اجهزة جديدة') ? (
                    <TableHead className="text-center">أجهزة جديدة</TableHead>
                  ) : null}
                  {!showTotalOnly && statusFilter.includes('اجهزه مستخدمة') ? (
                    <TableHead className="text-center">أجهزة مستخدمة</TableHead>
                  ) : null}
                  <TableHead className="text-center">التفاصيل</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSummaryData.length > 0 ? (
                  filteredSummaryData.map((item) => {
                    // Extract manufacturer name from model
                    const modelParts = item.model.split(' ');
                    const manufacturer = modelParts[0];
                    const modelName = modelParts.slice(1).join(' ');
                    
                    return (
                      <TableRow key={item.model} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{manufacturer}</TableCell>
                        <TableCell>{modelName}</TableCell>
                        <TableCell className="text-center font-semibold">{item.total}</TableCell>
                        {statusFilter.includes('متاح للبيع') || statusFilter.length === 0 ? (
                          <TableCell className="text-center">
                            <Badge variant="outline" className={`${item.available > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' : ''}`}>
                              {item.available}
                            </Badge>
                          </TableCell>
                        ) : null}
                        {statusFilter.includes('تحتاج صيانة') || statusFilter.length === 0 ? (
                          <TableCell className="text-center">
                            <Badge variant="outline" className={`${item.maintenance > 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100' : ''}`}>
                              {item.maintenance}
                            </Badge>
                          </TableCell>
                        ) : null}
                        {statusFilter.includes('قيد الإصلاح') || statusFilter.length === 0 ? (
                          <TableCell className="text-center">
                            <Badge variant="outline" className={`${item.inRepair > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100' : ''}`}>
                              {item.inRepair}
                            </Badge>
                          </TableCell>
                        ) : null}
                        {statusFilter.includes('معيب') || statusFilter.includes('تالف') || statusFilter.length === 0 ? (
                          <TableCell className="text-center">
                            <Badge variant="outline" className={`${(item.defective + item.damaged) > 0 ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100' : ''}`}>
                              {item.defective + item.damaged}
                            </Badge>
                          </TableCell>
                        ) : null}
                        {statusFilter.includes('اجهزة جديدة') ? (
                          <TableCell className="text-center">
                            <Badge variant="outline" className={`${(item.newDevices?.filter(d => d.status !== 'مباع').length || 0) > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100' : ''}`}>
                              {item.newDevices?.filter(d => d.status !== 'مباع').length || 0}
                            </Badge>
                          </TableCell>
                        ) : null}
                        {statusFilter.includes('اجهزه مستخدمة') ? (
                          <TableCell className="text-center">
                            <Badge variant="outline" className={`${(item.usedDevices?.filter(d => d.status !== 'مباع').length || 0) > 0 ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100' : ''}`}>
                              {item.usedDevices?.filter(d => d.status !== 'مباع').length || 0}
                            </Badge>
                          </TableCell>
                        ) : null}
                        <TableCell>
                          <div className="flex justify-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleOpenDetailsModal(item)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      لا توجد بيانات تطابق الفلاتر المحددة.
                    </TableCell>
                  </TableRow>
                )}
                {/* صف الإجمالي في نهاية الجدول */}
                <TableRow className="bg-muted/40 font-bold border-t-2 border-primary/30">
                  <TableCell colSpan={2} className="text-right">الإجمالي</TableCell>
                  <TableCell className="text-center">
                    {filteredSummaryData.reduce((sum, item) => sum + (item.total - item.sold), 0)}
                  </TableCell>
                  {!showTotalOnly && (statusFilter.includes('متاح للبيع') || statusFilter.length === 0) ? (
                    <TableCell className="text-center">
                      <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                        {filteredSummaryData.reduce((sum, item) => sum + item.available, 0)}
                      </Badge>
                    </TableCell>
                  ) : null}
                  {!showTotalOnly && (statusFilter.includes('تحتاج صيانة') || statusFilter.length === 0) ? (
                    <TableCell className="text-center">
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100">
                        {filteredSummaryData.reduce((sum, item) => sum + item.maintenance, 0)}
                      </Badge>
                    </TableCell>
                  ) : null}
                  {!showTotalOnly && (statusFilter.includes('قيد الإصلاح') || statusFilter.length === 0) ? (
                    <TableCell className="text-center">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
                        {filteredSummaryData.reduce((sum, item) => sum + item.inRepair, 0)}
                      </Badge>
                    </TableCell>
                  ) : null}
                  {!showTotalOnly && (statusFilter.includes('معيب') || statusFilter.includes('تالف') || statusFilter.length === 0) ? (
                    <TableCell className="text-center">
                      <Badge variant="outline" className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100">
                        {filteredSummaryData.reduce((sum, item) => sum + item.defective + item.damaged, 0)}
                      </Badge>
                    </TableCell>
                  ) : null}
                  {!showTotalOnly && statusFilter.includes('اجهزة جديدة') ? (
                    <TableCell className="text-center">
                      <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100">
                        {filteredSummaryData.reduce((sum, item) => sum + (item.newDevices?.filter(d => d.status !== 'مباع').length || 0), 0)}
                      </Badge>
                    </TableCell>
                  ) : null}
                  {!showTotalOnly && statusFilter.includes('اجهزه مستخدمة') ? (
                    <TableCell className="text-center">
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
                        {filteredSummaryData.reduce((sum, item) => sum + (item.usedDevices?.filter(d => d.status !== 'مباع').length || 0), 0)}
                      </Badge>
                    </TableCell>
                  ) : null}
                  
                  <TableCell></TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
          
          {/* تذييل النافذة */}
          <div className="flex items-center justify-between pt-4 border-t">          <div className="flex items-center">
            <div className="text-sm text-muted-foreground mr-4">
              عدد الموديلات: {filteredSummaryData.length}
            </div>
            <div className="text-sm text-muted-foreground font-semibold">
              إجمالي الأجهزة المتوفرة: {totalInventory} | إجمالي كل الأجهزة: {filteredSummaryData.reduce((sum, item) => sum + item.total, 0)}
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => handleExport('pdf')}
            >
              <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
            </Button>
            <Button
              variant="outline"
              onClick={() => handleExport('excel')}
            >
              <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
            </Button>
              <DialogClose asChild>
                <Button variant="outline">إغلاق</Button>
              </DialogClose>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}